{"name": "monexa-front-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run dev", "dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@radix-ui/react-slot": "^1.2.2", "@reduxjs/toolkit": "^2.5.1", "@tailwindcss/vite": "^4.0.8", "axios": "^1.7.9", "clsx": "^2.1.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.2.0", "react-select-country-list": "^2.2.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.0.8"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "tw-animate-css": "^1.3.0", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}}