import { Route, Routes } from "react-router-dom";
import BaseLayout from "@/app/layout/BaseLayout.tsx";
import AccountsPage from "@/pages/accounts/ui";
import CardsPage from "@/pages/cards/ui";
import TransactionsPage from "@/pages/transactions/ui";
import InvoicePage from "@/pages/invoice/ui";
import IbanPage from "@/pages/iban/ui";
import UserManagementPage from "@/pages/user-management/ui";
import ProfileSettingsPage from "@/pages/profile-settings/ui";
import AuthLayout from "@/app/layout/AuthLayout.tsx";
import Register from "@/pages/auth/Register.tsx";
import Login from "@/pages/auth/Login.tsx";
// import KYC from "@/pages/kyc";
// import KYB from "@/pages/kyb";
import OverviewPage from "@/pages/overview/ui/OverviewPage.tsx";
import AuthResend from "@/pages/auth/AuthResend.tsx";
import VerifyEmail from "@/pages/auth/VerifyEmail.tsx";
import ProtectedRoute from "@/app/provider/ProtectedRoute.tsx";
import VerificationFlow from "@/pages/verificationFlow/VerificationFlow.tsx";

const RouterProvider = () => {

    return (
        <Routes>
            <Route element={<AuthLayout />}>
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/auth-redirect" element={<AuthResend />} />
                <Route path="/verify-email" element={<VerifyEmail />} />
            </Route>

            <Route element={<ProtectedRoute />}>
                <Route path="/" element={<BaseLayout />}>
                    <Route index element={<OverviewPage />} />
                    <Route path="accounts" element={<AccountsPage />} />
                    <Route path="cards" element={<CardsPage />} />
                    <Route path="transactions" element={<TransactionsPage />} />
                    <Route path="invoices" element={<InvoicePage />} />
                    <Route path="iban" element={<IbanPage />} />
                    <Route path="user-management" element={<UserManagementPage />} />
                    <Route path="profile-settings" element={<ProfileSettingsPage />} />
                    <Route path="/verification" element={<VerificationFlow />} />
                </Route>
            </Route>
        </Routes>
    );
};

export default RouterProvider;
