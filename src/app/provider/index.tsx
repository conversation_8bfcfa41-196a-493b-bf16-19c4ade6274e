import { BrowserRouter } from "react-router-dom";
import RouterProvider from "./RouterProvider";
import StoreProvider from "@/app/provider/StoreProvider.tsx";
import "../index.css";
import {Toaster} from "react-hot-toast";

const Provider = () => {

    return (
        <StoreProvider>
            <BrowserRouter>
                <Toaster position="top-right" />
                <RouterProvider />
            </BrowserRouter>
        </StoreProvider>
    );
}

export default Provider;
