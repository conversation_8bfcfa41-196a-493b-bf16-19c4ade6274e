import { Navigate, Outlet } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { setCredentials } from "@/entities/auth/model/authSlice";
import { fetchProfile } from "@/entities/auth/model/profileSlice";
import { useEffect, useRef } from "react";

export default function ProtectedRoute() {
    const dispatch = useDispatch();
    const token = localStorage.getItem("access_token");
    const refreshToken = localStorage.getItem("refresh_token");

    const isAuthenticated = useSelector((state: RootState) => state.auth.isAuthenticated);
    const profile = useSelector((state: RootState) => state.profile.data);

    const alreadyRestored = useRef(false);


    useEffect(() => {
        if (token && !isAuthenticated) {
            dispatch(setCredentials({ access_token: token, refresh_token: refreshToken || undefined }));
        }

        if (token && !profile && !alreadyRestored.current) {
            dispatch(fetchProfile());
            alreadyRestored.current = true;
        }
    }, [token, isAuthenticated, profile, dispatch]);

    // if (!token) {
    //     return <Navigate to="/login" replace />;
    // }



    return <Outlet />;
}
