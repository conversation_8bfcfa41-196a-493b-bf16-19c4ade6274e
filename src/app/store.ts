import { configureStore } from "@reduxjs/toolkit";
import authSlice from "@/entities/auth/model/authSlice.ts";
import profileSlice from "@/entities/auth/model/profileSlice.ts";
import verificationSlice from "@/features/verification/model/verificationSlice.ts";
import kycSlice from "@/features/kyc/model/kycSlice.ts";
import kybSlice from "@/features/kyb/model/kybSlice.ts";

export const store = configureStore({
    reducer: {
        auth: authSlice,
        profile: profileSlice,
        verification: verificationSlice,
        kyc: kycSlice,
        kyb: kybSlice,
    },
});


export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
