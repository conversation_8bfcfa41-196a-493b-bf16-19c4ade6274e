import Header from "@/widgets/header/ui";
import SideBar from "@/widgets/sidebar/ui";
import { Outlet } from "react-router-dom";

const BaseLayout = () => {
    return (
        <div className="bg-m-dark">
           <div className="flex h-screen ">
               <SideBar />
               <div className="flex flex-col flex-1 rounded-lg my-2 mr-2 bg-gray-200 dark:bg-m-dark-muted">
                   <Header />
                   <main className="flex-1 px-8 py-6 overflow-auto">
                       <Outlet />
                   </main>
               </div>
           </div>
        </div>
    );
};

export default BaseLayout;
