import React from "react";

interface AccountCardProps {
    id: string;
    balance: string;
    style: string;
}

export const AccountCard: React.FC<AccountCardProps> = ({ id, balance, style = '' }) => {
    return (
        <div className={`bg-m-dark text-white p-4 rounded-2xl w-full max-w-[300px] h-[160px] cursor-pointer ${style}`}>
            <p className='text-white/70 mb-1'>Primary account</p>
            <p className="bg-m-violet w-max px-2 rounded-lg">ID {id}</p>
            <p className="text-2xl text-end">{balance}</p>
        </div>
    );
};
