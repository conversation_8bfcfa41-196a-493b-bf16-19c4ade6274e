import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import axiosInstance from "@/shared/api/axiosInstance.ts";

export interface Profile {
    name: string;
    email: string;
    phone_number: string;
    address: string;
    date_of_birth: string;
    role: "owner" | "admin" | string;
    verification_statuses: {
        kyc_status: string;
        kyb_status: string;
    };
}

interface ProfileState {
    data: Profile | null;
    loading: boolean;
    error: string | null;
}

const initialState: ProfileState = {
    data: null,
    loading: false,
    error: null,
};

export const fetchProfile = createAsyncThunk<Profile>(
    "profile/fetchProfile",
    async (_, { rejectWithValue }) => {
        try {
            const response = await axiosInstance.get("/api/v1/profile/me"); // замените на реальный URL
            return response.data;
        } catch (error: any) {
            return rejectWithValue(error.response?.data || "Ошибка при получении профиля");
        }
    }
);


export const updateProfile = createAsyncThunk<
    Profile,                           // что вернёт
    Partial<Profile> & { id: string }, // что получит: поля + id
    { rejectValue: string }
>(
    "profile/updateProfile",
    async ({...payload }, { rejectWithValue }) => {
        try {
            /* backend, как правило, ждёт PATCH; меняйте на PUT, если нужно */
            const { data } = await axiosInstance.patch(`/api/v1/profile/`, payload);
            return data;                    // вернёт свежий профиль
        } catch (err: any) {
            return rejectWithValue(err.response?.data || "Failed to update profile");
        }
    }
);

const profileSlice = createSlice({
    name: "profile",
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchProfile.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(fetchProfile.fulfilled, (state, action) => {
                state.loading = false;
                state.data = action.payload;
            })
            .addCase(fetchProfile.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })

            .addCase(updateProfile.pending,  (s) => { s.loading = true;  s.error = null; })
            .addCase(updateProfile.fulfilled,(s, a) => { s.loading = false; s.data = a.payload; })
            .addCase(updateProfile.rejected, (s, a) => { s.loading = false; s.error = a.payload as string; });
            },
});

export default profileSlice.reducer;
