export interface User {
    id: string;
    email: string;
}


export interface AuthState {
    access_token: string | null;
    user: User | null;
    isAuthenticated: boolean;
    loading: boolean;
    error: string | null;
    registrationSent: boolean;
}

// Тип данных для входа
export interface LoginPayload {
    email: string;
    password: string;
}

// Тип данных для регистрации
export interface RegisterPayload {
    company_trading_name: string;
    country: string;
    email: string;
    contact_phone: string;
    password: string;
    selected_product: string;  // если у вас читается один продукт
}

export interface LoginState {
    access_token: string | null;
    refresh_token?: string
}

// Тип ответа от бэкенда
export interface AuthResponse {
    user: string;
    token: string;
}
