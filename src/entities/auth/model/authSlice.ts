import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import axiosInstance from "@/shared/api/axiosInstance"; // ✅ Используем общий инстанс axios
import type { AuthState, RegisterPayload, LoginState } from "./types";
import axios, {isAxiosError} from "axios";
import toast from "react-hot-toast";
import {fetchProfile} from "@/entities/auth/model/profileSlice.ts";


const initialState: AuthState = {
    access_token: localStorage.getItem("access_token"),
    user: null,
    isAuthenticated: !!localStorage.getItem("access_token"),
    loading: false,
    error: null,
    registrationSent: false,   // новый флаг
};


export const register = createAsyncThunk<
    void,
    RegisterPayload,
    { rejectValue: string }
>(
    "auth/register",
    async (payload, { rejectWithValue }) => {
        try {
            await axiosInstance.post("api/v1/company/register", payload);
        } catch (err: unknown) {
            if (isAxiosError(err)) {
                console.log(err.response?.data?.error, "err");
                toast.error("The confirmation email has already been sent. Please wait or check your email")
                return rejectWithValue(err.response?.data?.message || "Registration failed");
            }
        }
    }
);



export const login = createAsyncThunk<
    { access_token: string; refresh_token?: string },
    LoginState,
    { rejectValue: string }
>(
    "auth/login",
    async (credentials, { dispatch, rejectWithValue }) => {
        try {
            const resp = await axios.post<{ access_token: string; refresh_token?: string }>(
                `https://back.monexa.uk/api/v1/company/login`,
                credentials
            );
            dispatch(setCredentials({
                access_token: resp.data.access_token,
                refresh_token: resp.data.refresh_token,
            }));

            await dispatch(fetchProfile());
            toast.success("Login successfully");
            return resp.data;
        } catch (err: unknown) {
            toast.error("Incorrect login or password");
            console.log(err, 'ERROR');
            return rejectWithValue("Incorrect login or password");
        }
    }
);



const authSlice = createSlice({
    name: "auth",
    initialState,
    reducers: {
        setCredentials(state, action: PayloadAction<{ access_token: string, refresh_token?: string }>) {
            console.log(action.payload, 'action.payload')
            state.access_token = action.payload.access_token;
            state.isAuthenticated = true;
            localStorage.setItem("access_token", action.payload.access_token);
            if (action.payload.refresh_token) {
                localStorage.setItem("refresh_token", action.payload.refresh_token);
            }
        },
        clearError(state) {
            state.error = null;
        },
        logout(state) {
            state.access_token = null;
            state.user = null;
            state.isAuthenticated = false;
            localStorage.removeItem("access_token");
            localStorage.removeItem("refresh_token");
        },
        resetRegistration(state) {
            state.registrationSent = false;
            state.error = null;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(register.pending, (state) => {
                state.loading = true;
                state.error = null;
                state.registrationSent = false;
            })
            .addCase(register.fulfilled, (state) => {
                state.loading = false;
                state.registrationSent = true;   // пометили, что запрос отправлен
            })
            .addCase(register.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload ?? "Registration error";
            })
            .addCase(login.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(login.fulfilled, (state, action) => {
                state.loading = false;
                state.isAuthenticated = true;
            })
            .addCase(login.rejected, (state, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
    },
});
export const { setCredentials, logout, resetRegistration } = authSlice.actions;


export default authSlice.reducer;
