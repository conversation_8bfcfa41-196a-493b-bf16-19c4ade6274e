import { forwardRef, useEffect, useState } from "react";
import { useFormContext } from "react-hook-form";

interface TextFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label: string;
    name: string;
    error?: string;
}

const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
    ({ label, name, error, type = "text", ...props }, ref) => {
        const form = useFormContext?.();
        const isInsideForm = !!form?.register;
        const value = isInsideForm ? form.watch(name) : props.value;
        const [isFocused, setIsFocused] = useState(false);
        const hasValue = !!value;

        return (
            <div className="relative w-full">
                <label
                    htmlFor={name}
                    className={`absolute left-3 transition-all pointer-events-none dark:text-white/65 ${
                        isFocused || hasValue ? "top-1 text-xs text-gray-500" : "top-4 text-gray-500"
                    }`}
                >
                    {label}
                </label>

                <input
                    id={name}
                    type={type}
                    name={name}
                    ref={ref}
                    onFocus={() => setIsFocused(true)}
                    onBlur={() => setIsFocused(false)}
                    {...(isInsideForm ? form.register(name) : {})}
                    {...props}
                    className={`w-full border rounded-lg px-3 pt-5 pb-2 outline-none bg-white dark:bg-[#1F1F1F] text-gray-900 dark:text-white ${
                        error ? "border-red-500" : "border-m-dark/20"
                    }`}
                    autoComplete="off"
                />

                {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </div>
        );
    }
);

export default TextField;
