import { forwardRef } from "react";

interface CheckboxCardProps {
    name: string;
    value: string;
    label: string;
    selected?: boolean;
    onChange?: () => void;
}

const CheckboxCard = forwardRef<HTMLInputElement, CheckboxCardProps>(
    ({ name, value, label, selected, onChange }, ref) => {
        return (
            <label
                className={`flex justify-between items-center w-full p-4 border border-m-dark/15 rounded-lg cursor-pointer transition `}
            >
                <span className="text-sm">{label}</span>
                <input
                    type="checkbox"
                    name={name}
                    value={value}
                    ref={ref}
                    checked={selected}
                    onChange={onChange}
                    className="hidden"
                />
                <div
                    className={`w-5 h-5 flex items-center justify-center rounded-full border ${
                        selected ? "bg-m-pink text-white " : "border-gray-300"
                    }`}
                >
                    {selected && <span className="text-xs">✔</span>}
                </div>
            </label>
        );
    }
);

export default CheckboxCard;
