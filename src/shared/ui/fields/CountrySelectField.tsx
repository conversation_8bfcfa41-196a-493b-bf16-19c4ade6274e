import { useEffect, useState } from "react";
import CustomSelect from "@/shared/ui/fields/CustomSelect.tsx";
import countryList from "react-select-country-list";

interface CountrySelectProps {
    name: string;
    label?: string;
    error?: string;
    setValue: (name: string, value: string) => void;
    watch: (name: string) => string;
}

const getFlagUrl = (countryCode: string) => {
    return `https://flagcdn.com/w40/${countryCode.toLowerCase()}.png`; // Используем официальный CDN флагов
};

const CountrySelect = ({ name, label, error,  setValue, watch }: CountrySelectProps) => {
    const [countries, setCountries] = useState<{ value: string; label: string; flag: string }[]>([]);

    useEffect(() => {
        const countryData = countryList().getData().map((country) => ({
            value: country.value,
            label: country.label,
            flag: getFlagUrl(country.value),
        }));

        setCountries(countryData);
    }, []);

    return <CustomSelect
        label={label}
        name={name}
        options={countries}
        error={error}
        value={watch(name)}
        onChange={(selectedValue) => setValue(name, selectedValue)}
    />;
};

export default CountrySelect;
