import { forwardRef } from "react";

interface CheckboxFieldProps {
    name: string;
    label: string;
    error?: string;
}

const CheckboxField = forwardRef<HTMLInputElement, CheckboxFieldProps>(
    ({ name, label, error, ...props }, ref) => {
        return (
            <div className="">
                <div className='flex items-center space-x-2'>
                    <input
                        type="checkbox"
                        name={name}
                        ref={ref}
                        {...props}
                        className="w-5 h-5 border rounded cursor-pointer"
                    />
                    <label className="text-sm text-gray-700">{label}</label>
                </div>

                {/* Ошибка */}
                {error && <p className="text-red-500 text-xs text-center">{error}</p>}
            </div>
        );
    }
);

export default CheckboxField;
