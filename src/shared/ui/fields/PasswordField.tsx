import { useState, forwardRef } from "react";

interface PasswordFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
    label: string;
    name: string;
    error?: string;
}

const PasswordField = forwardRef<HTMLInputElement, PasswordFieldProps>(
    ({ label, name, error, ...props }, ref) => {
        const [isVisible, setIsVisible] = useState(false);
        const [isFocused, setIsFocused] = useState(false);
        const [hasValue, setHasValue] = useState(!!props.value);

        const handleFocus = () => setIsFocused(true);
        const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
            setIsFocused(false);
            setHasValue(!!event.target.value);
        };

        return (
            <div className="relative w-full">
                <label
                    className={`absolute left-3 transition-all ${
                        isFocused || hasValue ? "top-1 text-xs text-gray-500" : "top-4 text-gray-500"
                    }`}
                >
                    {label}
                </label>

                {/* Поле ввода */}
                <input
                    type={isVisible ? "text" : "password"}
                    name={name}
                    ref={ref}
                    {...props}
                    onFocus={handleFocus}
                    onBlur={handleBlur}
                    className={`w-full border rounded-lg px-3 pt-5 pb-2 outline-none bg-white dark:bg-[#1F1F1F] text-gray-900 dark:text-white ${
                        error ? "border-red-500" : "border-m-dark/20"
                    }`}
                />

                {/* Кнопка показать/скрыть пароль */}
                <button
                    type="button"
                    onClick={() => setIsVisible(!isVisible)}
                    className="absolute right-3 top-4 text-gray-500 hover:text-gray-700 cursor-pointer"
                >
                    <img
                        src={isVisible ? "/images/icons/eye-closed.svg" : "/images/icons/eye.svg"}
                        alt={isVisible ? "Hide Password" : "Show Password"}
                        className="w-5 h-5"
                    />
                </button>

                {/* Ошибка */}
                {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </div>
        );
    }
);

export default PasswordField;
