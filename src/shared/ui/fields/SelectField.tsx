import { forwardRef, useState } from "react";

interface SelectFieldProps {
    label: string;
    name: string;
    options: { value: string; label: string }[];
    error?: string;
}

const SelectField = forwardRef<HTMLSelectElement, SelectFieldProps>(
    ({ label, name, options, error, onChange, ...props }, ref) => {
        const [isSelected, setIsSelected] = useState(false);

        const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
            setIsSelected(event.target.value !== "");
            if (onChange) onChange(event); // Вызов переданного onChange
        };

        return (
            <div className="relative w-full">
                <label
                    className={`absolute left-3 transition-all pointer-events-none
                             ${isSelected ? "top-1 text-xs text-white" : "top-4 text-gray-500"}
                             peer-focus:top-1 peer-focus:text-xs peer-focus:text-white`}>
                    {label}
                </label>

                {/* Выпадающий список */}
                <select
                    name={name}
                    ref={ref}
                    {...props}
                    onChange={handleChange}
                    className={`w-full border rounded-lg px-2 pt-4 pb-4 outline-none cursor-pointer bg-white dark:bg-[#1F1F1F] text-gray-900 dark:text-white appearance-none ${
                        error ? "border-red-500" : "border-m-dark/20"
                    }`}
                >
                    <option value=""></option>
                    {options.map((opt) => (
                        <option key={opt.value} value={opt.value}>
                            {opt.label}
                        </option>
                    ))}
                </select>

                {/* Ошибка */}
                {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
            </div>
        );
    }
);

export default SelectField;
