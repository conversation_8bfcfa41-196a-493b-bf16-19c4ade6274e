import { useState, useEffect, useRef } from "react";

interface Option {
    value: string;
    label: string;
    flag?: string; // URL картинки-флага
}

interface CustomSelectProps {
    label?: string;
    name: string;
    options: Option[];
    error?: string;
    value?: string;
    onChange: (value: string) => void;
}

const CustomSelect = ({ label, options, error, onChange }: CustomSelectProps) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [isFocused, setIsFocused] = useState(false);
    const selectRef = useRef<HTMLDivElement>(null);

    const filteredOptions = options.filter((option) =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Закрываем список при клике вне
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
                setIsOpen(false);
                setIsFocused(false);
            }
        };

        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, []);

    const inputId = `select-${name}`;

    return (
        <div className="w-full relative" ref={selectRef}>
            <div className="relative">
                {label && (
                    <label
                        className={`absolute left-3 transition-all dark:text-white/65 ${
                            isFocused || searchTerm
                                ? "top-1 text-xs text-gray-500"
                                : "top-4 text-gray-500"
                        }`}
                    >
                        {label}
                    </label>
                )}

                <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onFocus={() => {
                        setIsOpen(true);
                        setIsFocused(true);
                    }}
                    className={`w-full bg-white dark:bg-[#1F1F1F] border outline-none border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white px-3 pb-2 rounded-lg pt-5
                    ${error ? "border-red-500" : "border-m-dark/20"}`}

                />

                {isOpen && filteredOptions.length > 0 && (
                    <ul className="absolute z-10 w-full bg-white dark:bg-[#1F1F1F] border border-gray-300 dark:border-gray-700 rounded-lg mt-1 max-h-48 overflow-y-auto shadow-lg">
                        {filteredOptions.map((option) => (
                            <li
                                key={option.value}
                                onClick={() => {
                                    setSearchTerm(option.label);
                                    onChange(option.value);
                                    setIsOpen(false);
                                    setIsFocused(false);
                                }}
                                className="p-3 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 dark:text-white flex items-center"
                            >
                                {option.flag && (
                                    <img src={option.flag} alt={option.label} className="w-5 h-5 mr-2 rounded-full object-cover" />
                                )}
                                {option.label}
                            </li>
                        ))}
                    </ul>
                )}
            </div>
            {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
        </div>
    );
};

export default CustomSelect;
