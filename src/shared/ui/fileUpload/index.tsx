import { useRef, useState } from "react";

interface FileUploadProps {
    label: string;
    onFileSelect: (file: File) => void;
    error?: string; // ⚠️ добавлено
}

const FileUpload: React.FC<FileUploadProps> = ({ label, onFileSelect, error }) => {
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [fileName, setFileName] = useState<string | null>(null);


    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files.length > 0) {
            const file = event.target.files[0];
            setFileName(file.name);
            onFileSelect(file);
        }
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        if (event.dataTransfer.files.length > 0) {
            const file = event.dataTransfer.files[0];
            setFileName(file.name);
            onFileSelect(file);
        }
    };

    return (
        <div className="flex flex-col w-full">
            <label className="block text-gray-600 mb-2 dark:text-white">{label}</label>
            <div
                className={`w-full border-2 rounded-xl h-40 flex flex-col items-center justify-center text-center cursor-pointer transition-colors
                    ${fileName
                    ? "border-green-400 bg-green-50 hover:border-green-500"
                    : error
                        ? "border-red-400 bg-red-50 hover:border-red-500"
                        : "border-dashed border-gray-400 bg-white dark:bg-gray-200 hover:border-m-violet"}
                `}
                onClick={() => fileInputRef.current?.click()}
                onDragOver={(e) => e.preventDefault()}
                onDrop={handleDrop}
            >
                <img
                    src={
                        fileName
                            ? "/images/icons/circle.png"
                            : "/images/icons/uploadFIle.svg"
                    }
                    alt="Upload"
                    className="w-8 h-8 mb-2"
                />
                <span className={
                    fileName ? "text-green-700 font-medium" :
                        error ? "text-red-600" : "text-m-blue-violet"
                }>
                    {fileName ? "Document selected" : "Upload document"}
                </span>
            </div>
            <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileChange}
            />

            {error && (
                <p className="mt-1 text-xs text-red-500">{error}</p>
            )}
        </div>
    );
};

export default FileUpload;
