import React from 'react';

interface Column<T> {
    key: keyof T | string;
    header: string;
    render?: (value: any, row: T) => React.ReactNode;
    sortable?: boolean;
    width?: string;
    align?: 'left' | 'center' | 'right';
}

interface TableProps<T> {
    data: T[];
    columns: Column<T>[];
    loading?: boolean;
    emptyMessage?: string;
    className?: string;
    onRowClick?: (row: T) => void;
    hoverable?: boolean;
}

function Table<T extends Record<string, any>>({
    data,
    columns,
    loading = false,
    emptyMessage = "No data available",
    className = "",
    onRowClick,
    hoverable = false
}: TableProps<T>) {
    const getNestedValue = (obj: T, path: string) => {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    const getAlignmentClass = (align?: string) => {
        switch (align) {
            case 'center':
                return 'text-center';
            case 'right':
                return 'text-right';
            default:
                return 'text-left';
        }
    };

    if (loading) {
        return (
            <div className={`bg-white dark:bg-m-dark rounded-xl shadow-sm ${className}`}>
                <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-m-pink"></div>
                </div>
            </div>
        );
    }

    return (
        <div className={`bg-white dark:bg-m-dark rounded-xl shadow-sm overflow-hidden ${className}`}>
            <div className="overflow-x-auto">
                <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-m-dark-muted">
                        <tr>
                            {columns.map((column, index) => (
                                <th
                                    key={index}
                                    className={`px-6 py-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider ${getAlignmentClass(column.align)}`}
                                    style={{ width: column.width }}
                                >
                                    {column.header}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-m-dark divide-y divide-gray-200 dark:divide-gray-700">
                        {data.length === 0 ? (
                            <tr>
                                <td colSpan={columns.length} className="px-6 py-12 text-center">
                                    <div className="text-gray-500 dark:text-gray-400">
                                        <p className="text-lg font-medium">No data found</p>
                                        <p className="text-sm mt-1">{emptyMessage}</p>
                                    </div>
                                </td>
                            </tr>
                        ) : (
                            data.map((row, rowIndex) => (
                                <tr
                                    key={rowIndex}
                                    className={`
                                        ${hoverable ? 'hover:bg-gray-50 dark:hover:bg-m-dark-muted/50 transition-colors' : ''}
                                        ${onRowClick ? 'cursor-pointer' : ''}
                                    `}
                                    onClick={() => onRowClick?.(row)}
                                >
                                    {columns.map((column, colIndex) => {
                                        const value = getNestedValue(row, column.key as string);
                                        const content = column.render ? column.render(value, row) : value;
                                        
                                        return (
                                            <td
                                                key={colIndex}
                                                className={`px-6 py-4 whitespace-nowrap ${getAlignmentClass(column.align)}`}
                                            >
                                                {content}
                                            </td>
                                        );
                                    })}
                                </tr>
                            ))
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
}

export default Table;
