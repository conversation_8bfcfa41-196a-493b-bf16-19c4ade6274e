import {useState, useRef, useEffect} from "react";
import { DayPicker } from "react-day-picker";
import 'react-day-picker/dist/style.css';
import { Controller, useFormContext } from "react-hook-form";
import {
    format as formatDate,
    parse  as parseDate,
    isValid as isValidDate
} from "date-fns";
import CalendarDropdown from "@/shared/ui/calendar/dropdown/CalendarDropdown.tsx";

export default function CalendarField({ name }: { name: string }) {
    const { control } = useFormContext();
    const [open, setOpen] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (e: MouseEvent) => {
            if (open && containerRef.current && !containerRef.current.contains(e.target as Node)) {
                setOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [open]);

    return (
        <Controller
            control={control}
            name={name}
            render={({ field, fieldState }) => {
                const parseInput = (value: string) => {
                    const tryFormats = ["yyyy-MM-dd", "yyyy MM dd", "yyyy/MM/dd"];
                    for (const fmt of tryFormats) {
                        const d = parseDate(value, fmt, new Date());
                        if (isValidDate(d)) return d;
                    }
                    return null;
                };

                const rawValue     = field.value ?? "";
                const parsed       = parseInput(rawValue);
                const displayValue = parsed
                    ? formatDate(parsed, "yyyy-MM-dd")
                    : rawValue;

                const selected = parsed ?? undefined;

                const onInput = (e: React.ChangeEvent<HTMLInputElement>) => {
                    const d = parseInput(e.target.value);
                    field.onChange(d ? formatDate(d, "yyyy-MM-dd") : e.target.value);
                    if (d) setOpen(false);
                };

                const onSelect = (d?: Date) => {
                    if (d) {
                        field.onChange(formatDate(d, "yyyy-MM-dd"));
                        setOpen(false);
                    }
                };

                return (
                    <div ref={containerRef} className="relative w-full">
                        <input
                            type="text"
                            placeholder="Date of birth"
                            value={displayValue}
                            onChange={onInput}
                            onFocus={() => setOpen(true)}
                            className={`w-full px-4 py-3 border rounded-lg bg-white text-base focus:outline-none ${
                                fieldState.error ? "border-red-500" : "border-gray-300"
                            }`}
                        />

                        {fieldState.error && (
                            <p className="text-sm text-red-500 mt-1">{fieldState.error.message}</p>
                        )}

                        {open && (
                            <div className="absolute z-50 mt-2 bg-white border rounded-lg shadow-md p-4">
                                <DayPicker
                                    mode="single"
                                    captionLayout="dropdown"
                                    selected={selected}
                                    onSelect={onSelect}
                                    fromYear={1930}
                                    toYear={new Date().getFullYear()}
                                    components={{ Dropdown: CalendarDropdown }}
                                    modifiersClassNames={{
                                        selected: "bg-rose-500 text-white",
                                        today:    "bg-rose-100",
                                    }}
                                />
                            </div>
                        )}
                    </div>
                );
            }}
        />
    );
}
