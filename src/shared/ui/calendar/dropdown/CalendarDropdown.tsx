// CalendarDropdown.tsx
import { useState, useRef, useEffect } from "react";
import { DropdownProps } from "react-day-picker";

export default function CalendarDropdown(props: DropdownProps) {
    const { value, options, onChange } = props;
    const [open, setOpen] = useState(false);
    const btnRef  = useRef<HTMLButtonElement>(null);
    const listRef = useRef<HTMLUListElement>(null);

    // Закрываем по клику вне
    useEffect(() => {
        const close = (e: MouseEvent) => {
            if (
                open &&
                !btnRef.current?.contains(e.target as Node) &&
                !listRef.current?.contains(e.target as Node)
            ) {
                setOpen(false);
            }
        };
        document.addEventListener("mousedown", close);
        return () => document.removeEventListener("mousedown", close);
    }, [open]);

    /* react-day-picker хочет именно ChangeEvent<HTMLSelectElement> */
    const change = (v: string) =>
        onChange?.({ target: { value: v } } as React.ChangeEvent<HTMLSelectElement>);

    return (
        <div className="relative inline-block text-sm">
            <button
                ref={btnRef}
                type="button"
                onClick={() => setOpen((o) => !o)}
                className="h-7 px-2 border rounded w-20 flex items-center justify-between"
            >
                {value}
                <svg className="w-3 h-3 ml-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5.23 7.21a.75.75 0 011.06.02L10 10.94l3.71-3.71a.75.75 0 111.06 1.06l-4.24 4.24a.75.75 0 01-1.06 0L5.25 8.29a.75.75 0 01-.02-1.08z" />
                </svg>
            </button>

            {open && (
                <ul
                    ref={listRef}
                    className="absolute z-50 mt-1 w-24 max-h-40 overflow-auto border bg-white rounded shadow"
                >
                    {options?.map((opt) => (
                        <li
                            key={opt.value}
                            className={`px-3 py-1 cursor-pointer hover:bg-gray-100 ${
                                opt.disabled ? "text-gray-400 cursor-not-allowed" : ""
                            } ${value?.toString() === opt.value.toString() ? "bg-indigo-50" : ""}`}
                            onClick={() => !opt.disabled && (change(opt.value.toString()), setOpen(false))}
                        >
                            {opt.label}
                        </li>
                    ))}
                </ul>
            )}
        </div>
    );
}
