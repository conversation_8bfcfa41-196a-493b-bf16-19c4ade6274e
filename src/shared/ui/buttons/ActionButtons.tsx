import React from "react";

interface ActionButtonProps {
    icon: string;
    label: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({ icon, label }) => {
    return (
        <button className="flex items-center gap-2 px-4 py-2 rounded-lg cursor-pointer border border-m-dark/65 bg-white text-black dark:bg-m-dark-muted dark:text-white">
            <img src={icon} alt={label} className="w-6 h-6 dark:invert" />
            {label}
        </button>
    );
};
