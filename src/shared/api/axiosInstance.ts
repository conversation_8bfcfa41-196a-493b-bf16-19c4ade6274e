import axios from "axios";

const API = import.meta.env.VITE_API_BASE_URL;

const axiosInstance = axios.create({
    baseURL: API,
    headers: {
        "Content-Type": "application/json",
    },
});

// === Request Interceptor ===
axiosInstance.interceptors.request.use((config) => {
    const token = localStorage.getItem("access_token");

    config.headers = config.headers || {};
    if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
    }
    config.headers["Accept-Language"] = "en";

    return config;
});

let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach((prom) => {
        if (error) prom.reject(error);
        else prom.resolve(token);
    });
    failedQueue = [];
};

// === Response Interceptor ===
axiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;
        const status = error.response?.status;

        if (status === 401 && !originalRequest._retry) {
            originalRequest._retry = true;

            if (isRefreshing) {
                return new Promise((resolve, reject) => {
                    failedQueue.push({ resolve, reject });
                }).then((token) => {
                    originalRequest.headers["Authorization"] = `Bearer ${token}`;
                    return axiosInstance(originalRequest);
                });
            }

            isRefreshing = true;
            const refreshToken = localStorage.getItem("refresh_token");

            try {
                const res = await axios.post(`${API}api/v1/company/refresh`, {
                    refresh_token: refreshToken,
                });

                const newToken = res.data.access_token;
                const newRefreshToken = res.data.refresh_token;
                localStorage.setItem("access_token", newToken);
                localStorage.setItem("refresh_token", newRefreshToken);

                processQueue(null, newToken);

                originalRequest.headers["Authorization"] = `Bearer ${newToken}`;
                return axiosInstance(originalRequest);
            } catch (refreshError) {
                processQueue(refreshError, null);
                localStorage.removeItem("access_token");
                localStorage.removeItem("refresh_token");
                window.location.replace("/login");
                return Promise.reject(refreshError);
            } finally {
                isRefreshing = false;
            }
        }

        if (status === 403) {
            console.error("Access forbidden");
        }

        return Promise.reject(error);
    }
);

export default axiosInstance;
