import React, {useEffect, useState} from "react";
import {SearchBar} from "@/features/search/ui/SearchBar.tsx";
import AccountTransactions from "@/widgets/account/account-transactions/AccountTransactions.tsx";
import AccountsTabs from "@/widgets/account/account-tabs/AccountsTabs.tsx";
import AccountSummary from "@/widgets/account/account-summary/AccountSummary.tsx";

const AccountsPage: React.FC = () => {

    const handleSearch = () => {
    }


    const [balance, setBalance] = useState<string>("$0.00");
    const [accounts, setAccounts] = useState<{ id: string; balance: string }[]>([]);
    const [transactions, setTransactions] = useState<{ id: number; name: string; amount: string; date: string; icons: string }[]>([]);
    const [chartData, setChartData] = useState<number[]>([]);


    useEffect(() => {

        setTimeout(() => {
            setBalance("$0");

            setAccounts([
                { id: "123456", balance: "$0.00" },
                { id: "123457", balance: "$0.00" },
                { id: "123458", balance: "$0.00" }
            ]);

            setTransactions([
                { id: 1, name: "KFC", amount: "+339 USD", date: "4.02.2025 12:33", icons: "/images/icons/food.svg" },
                { id: 2, name: "Withdrawal", amount: "-8.339 EUR", date: "4.02.2025 08:53", icons: "/images/icons/transfer.svg" },
                { id: 3, name: "Withdrawal", amount: "-8.339 EUR", date: "4.02.2025 14:30", icons: "/images/icons/transfer.svg" },
                { id: 4, name: "Withdrawal", amount: "-8.339 EUR", date: "4.02.2025 09:08", icons: "/images/icons/transfer.svg" },
                { id: 5, name: "KFC", amount: "+339 USD", date: "4.02.2025 12:33", icons: "/images/icons/food.svg" },
                { id: 6, name: "KFC", amount: "+339 USD", date: "5.02.2025 07:33", icons: "/images/icons/food.svg" },
                { id: 7, name: "KFC", amount: "+250 USD", date: "12.02.2025 09:00", icons: "/images/icons/food.svg" },
                { id: 8, name: "KFC", amount: "+339 USD", date: "15.02.2025 11:42", icons: "/images/icons/food.svg" },
                // { id: 9, name: "KFC", amount: "+339 USD", date: "15.02.2025 11:42", icons: "/images/icons/food.svg" },
                // { id: 10, name: "KFC", amount: "+339 USD", date: "15.02.2025 11:42", icons: "/images/icons/food.svg" },
                // { id: 11, name: "KFC", amount: "+339 USD", date: "15.02.2025 11:42", icons: "/images/icons/food.svg" },
                // { id: 12, name: "KFC", amount: "+339 USD", date: "15.02.2025 11:42", icons: "/images/icons/food.svg" },


            ]);
            setChartData([1200, 400, 150, 520, 1000, 200, 1000, 500]);
        }, 100);
    }, []);

    return (
        <div className="">
            <SearchBar onSearch={handleSearch}/>
            <AccountsTabs accounts={accounts}/>
            <div className='flex gap-6 mt-7 w-full'>
                <AccountSummary />
               <div className='w-1/2'>
                   <AccountTransactions transactions={transactions}/>
               </div>
            </div>
        </div>
    );
};

export default AccountsPage;