import { create } from "zustand";

interface OverviewState {
    accounts: { id: string; balance: string }[];
    transactions: { id: number; name: string; amount: string; date: string }[];
    setAccounts: (accounts: OverviewState["accounts"]) => void;
    setTransactions: (transactions: OverviewState["transactions"]) => void;
}

export const useOverviewStore = create<OverviewState>((set) => ({
    accounts: [],
    transactions: [],
    setAccounts: (accounts) => set({ accounts }),
    setTransactions: (transactions) => set({ transactions })
}));
