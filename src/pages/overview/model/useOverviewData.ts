import { useEffect, useState } from "react";

export const useOverviewData = () => {
    const [accounts, setAccounts] = useState([]);
    const [transactions, setTransactions] = useState([]);

    useEffect(() => {
        // Симуляция API-запроса
        setTimeout(() => {
            setAccounts([
                { id: "123456", balance: "$0.00" },
                { id: "123457", balance: "$100.00" },
                { id: "123458", balance: "$50.00" }
            ]);

            setTransactions([
                { id: 1, name: "K<PERSON>", amount: "+339 USD", date: "4.02.2025 12:33" },
                { id: 2, name: "Withdrawal", amount: "-8.339 EUR", date: "4.02.2025 12:33" },
            ]);
        }, 500);
    }, []);

    return { accounts, transactions };
};
