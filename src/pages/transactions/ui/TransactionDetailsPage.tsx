import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiDownload, FiCopy, FiCheck } from 'react-icons/fi';

interface TransactionDetails {
    id: string;
    description: string;
    amount: string;
    currency: string;
    date: string;
    time: string;
    status: 'completed' | 'pending' | 'failed';
    type: 'credit' | 'debit';
    category: string;
    account: string;
    reference: string;
    fromAccount?: string;
    toAccount?: string;
    fee?: string;
    exchangeRate?: string;
    notes?: string;
    merchantInfo?: {
        name: string;
        location: string;
        category: string;
    };
}

const TransactionDetailsPage: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [transaction, setTransaction] = useState<TransactionDetails | null>(null);
    const [loading, setLoading] = useState(true);
    const [copiedField, setCopiedField] = useState<string | null>(null);

    useEffect(() => {
        // Mock API call - replace with actual API
        const fetchTransactionDetails = async () => {
            setLoading(true);
            
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // Mock data based on ID
            const mockTransaction: TransactionDetails = {
                id: id || 'TXN001',
                description: 'Payment to KFC Restaurant',
                amount: '339.00',
                currency: 'USD',
                date: '2025-01-15',
                time: '12:33:45',
                status: 'completed',
                type: 'debit',
                category: 'Food & Dining',
                account: 'Main Account (**** 1234)',
                reference: 'REF123456789',
                fromAccount: 'Main Account (**** 1234)',
                toAccount: 'KFC Restaurant',
                fee: '2.50',
                notes: 'Lunch payment via mobile app',
                merchantInfo: {
                    name: 'KFC Restaurant',
                    location: '123 Main Street, New York, NY',
                    category: 'Fast Food'
                }
            };
            
            setTransaction(mockTransaction);
            setLoading(false);
        };

        if (id) {
            fetchTransactionDetails();
        }
    }, [id]);

    const handleCopy = async (text: string, field: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopiedField(field);
            setTimeout(() => setCopiedField(null), 2000);
        } catch (err) {
            console.error('Failed to copy text: ', err);
        }
    };

    const handleDownloadReceipt = () => {
        // Download receipt functionality
        console.log('Downloading receipt for transaction:', id);
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'completed':
                return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
            case 'pending':
                return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
            case 'failed':
                return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
            default:
                return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
        }
    };

    const getTypeColor = (type: string) => {
        return type === 'credit' 
            ? 'text-green-600 dark:text-green-400' 
            : 'text-red-600 dark:text-red-400';
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-96">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-m-pink"></div>
            </div>
        );
    }

    if (!transaction) {
        return (
            <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400">Transaction not found</p>
                <button
                    onClick={() => navigate('/transactions')}
                    className="mt-4 px-4 py-2 bg-m-pink text-white rounded-lg hover:bg-m-pink/90 transition-colors"
                >
                    Back to Transactions
                </button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <button
                        onClick={() => navigate('/transactions')}
                        className="p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-m-dark-muted transition-colors"
                    >
                        <FiArrowLeft className="dark:text-white" size={20} />
                    </button>
                    <div>
                        <h1 className="text-2xl font-semibold dark:text-white">Transaction Details</h1>
                        <p className="text-gray-500 dark:text-gray-400">Transaction ID: {transaction.id}</p>
                    </div>
                </div>
                <button
                    onClick={handleDownloadReceipt}
                    className="flex items-center gap-2 px-4 py-2 bg-m-pink text-white rounded-lg hover:bg-m-pink/90 transition-colors"
                >
                    <FiDownload size={16} />
                    Download Receipt
                </button>
            </div>

            {/* Transaction Summary Card */}
            <div className="bg-white dark:bg-m-dark rounded-xl p-6 shadow-sm">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h2 className="text-xl font-semibold dark:text-white mb-2">
                            {transaction.description}
                        </h2>
                        <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(transaction.status)}`}>
                            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                        </span>
                    </div>
                    <div className="text-right">
                        <div className={`text-3xl font-bold ${getTypeColor(transaction.type)}`}>
                            {transaction.type === 'credit' ? '+' : '-'}{transaction.amount} {transaction.currency}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                            {transaction.date} at {transaction.time}
                        </div>
                    </div>
                </div>
            </div>

            {/* Transaction Details */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Basic Information */}
                <div className="bg-white dark:bg-m-dark rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold dark:text-white mb-4">Transaction Information</h3>
                    <div className="space-y-4">
                        <div className="flex justify-between items-center">
                            <span className="text-gray-600 dark:text-gray-400">Reference Number</span>
                            <div className="flex items-center gap-2">
                                <span className="dark:text-white font-mono">{transaction.reference}</span>
                                <button
                                    onClick={() => handleCopy(transaction.reference, 'reference')}
                                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
                                >
                                    {copiedField === 'reference' ? <FiCheck size={16} /> : <FiCopy size={16} />}
                                </button>
                            </div>
                        </div>
                        
                        <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Category</span>
                            <span className="dark:text-white">{transaction.category}</span>
                        </div>
                        
                        <div className="flex justify-between">
                            <span className="text-gray-600 dark:text-gray-400">Account</span>
                            <span className="dark:text-white">{transaction.account}</span>
                        </div>
                        
                        {transaction.fee && (
                            <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-gray-400">Transaction Fee</span>
                                <span className="dark:text-white">{transaction.fee} {transaction.currency}</span>
                            </div>
                        )}
                        
                        {transaction.exchangeRate && (
                            <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-gray-400">Exchange Rate</span>
                                <span className="dark:text-white">{transaction.exchangeRate}</span>
                            </div>
                        )}
                    </div>
                </div>

                {/* Transfer Details */}
                <div className="bg-white dark:bg-m-dark rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold dark:text-white mb-4">Transfer Details</h3>
                    <div className="space-y-4">
                        {transaction.fromAccount && (
                            <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-gray-400">From</span>
                                <span className="dark:text-white">{transaction.fromAccount}</span>
                            </div>
                        )}
                        
                        {transaction.toAccount && (
                            <div className="flex justify-between">
                                <span className="text-gray-600 dark:text-gray-400">To</span>
                                <span className="dark:text-white">{transaction.toAccount}</span>
                            </div>
                        )}
                        
                        {transaction.merchantInfo && (
                            <>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">Merchant</span>
                                    <span className="dark:text-white">{transaction.merchantInfo.name}</span>
                                </div>
                                <div className="flex justify-between">
                                    <span className="text-gray-600 dark:text-gray-400">Location</span>
                                    <span className="dark:text-white text-right">{transaction.merchantInfo.location}</span>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            </div>

            {/* Notes */}
            {transaction.notes && (
                <div className="bg-white dark:bg-m-dark rounded-xl p-6 shadow-sm">
                    <h3 className="text-lg font-semibold dark:text-white mb-4">Notes</h3>
                    <p className="text-gray-600 dark:text-gray-400">{transaction.notes}</p>
                </div>
            )}
        </div>
    );
};

export default TransactionDetailsPage;
