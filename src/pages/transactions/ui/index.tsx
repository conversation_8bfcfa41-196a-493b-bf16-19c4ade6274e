import React, { useState, useEffect } from 'react';
import { SearchBar } from "@/features/search/ui/SearchBar";
import TransactionTable from "@/features/transaction/ui/TransactionTable";
import TransactionFilters from "@/features/transaction/ui/TransactionFilters";

interface Transaction {
    id: string;
    description: string;
    amount: string;
    currency: string;
    date: string;
    time: string;
    status: 'completed' | 'pending' | 'failed';
    type: 'credit' | 'debit';
    category: string;
    account: string;
    reference: string;
}

const TransactionsPage: React.FC = () => {
    const [transactions, setTransactions] = useState<Transaction[]>([]);
    const [filteredTransactions, setFilteredTransactions] = useState<Transaction[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [filters, setFilters] = useState({
        status: 'all',
        type: 'all',
        dateRange: 'all',
        category: 'all'
    });

    // Mock data - replace with API call
    useEffect(() => {
        const mockTransactions: Transaction[] = [
            {
                id: 'TXN001',
                description: 'Payment to KFC Restaurant',
                amount: '339.00',
                currency: 'USD',
                date: '2025-01-15',
                time: '12:33',
                status: 'completed',
                type: 'debit',
                category: 'Food & Dining',
                account: 'Main Account',
                reference: 'REF123456'
            },
            {
                id: 'TXN002',
                description: 'ATM Withdrawal',
                amount: '8339.00',
                currency: 'EUR',
                date: '2025-01-15',
                time: '08:53',
                status: 'completed',
                type: 'debit',
                category: 'Cash Withdrawal',
                account: 'Savings Account',
                reference: 'REF123457'
            },
            {
                id: 'TXN003',
                description: 'Salary Deposit',
                amount: '5000.00',
                currency: 'USD',
                date: '2025-01-14',
                time: '09:00',
                status: 'completed',
                type: 'credit',
                category: 'Salary',
                account: 'Main Account',
                reference: 'REF123458'
            },
            {
                id: 'TXN004',
                description: 'Online Purchase - Amazon',
                amount: '156.99',
                currency: 'USD',
                date: '2025-01-14',
                time: '15:22',
                status: 'pending',
                type: 'debit',
                category: 'Shopping',
                account: 'Main Account',
                reference: 'REF123459'
            },
            {
                id: 'TXN005',
                description: 'Transfer to John Doe',
                amount: '250.00',
                currency: 'USD',
                date: '2025-01-13',
                time: '11:45',
                status: 'failed',
                type: 'debit',
                category: 'Transfer',
                account: 'Main Account',
                reference: 'REF123460'
            }
        ];
        setTransactions(mockTransactions);
        setFilteredTransactions(mockTransactions);
    }, []);

    // Filter and search logic
    useEffect(() => {
        let filtered = transactions;

        // Apply search filter
        if (searchQuery) {
            filtered = filtered.filter(transaction =>
                transaction.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                transaction.reference.toLowerCase().includes(searchQuery.toLowerCase()) ||
                transaction.category.toLowerCase().includes(searchQuery.toLowerCase())
            );
        }

        // Apply status filter
        if (filters.status !== 'all') {
            filtered = filtered.filter(transaction => transaction.status === filters.status);
        }

        // Apply type filter
        if (filters.type !== 'all') {
            filtered = filtered.filter(transaction => transaction.type === filters.type);
        }

        // Apply category filter
        if (filters.category !== 'all') {
            filtered = filtered.filter(transaction => transaction.category === filters.category);
        }

        setFilteredTransactions(filtered);
    }, [transactions, searchQuery, filters]);

    const handleSearch = (query: string) => {
        setSearchQuery(query);
    };

    const handleFilterChange = (newFilters: typeof filters) => {
        setFilters(newFilters);
    };

    return (
        <div className="space-y-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                <SearchBar onSearch={handleSearch} />
                <TransactionFilters filters={filters} onFilterChange={handleFilterChange} />
            </div>

            <TransactionTable transactions={filteredTransactions} />
        </div>
    );
};

export default TransactionsPage;