import { useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setCredentials } from "@/entities/auth/model/authSlice";
import {fetchProfile} from "@/entities/auth/model/profileSlice.ts";

export default function AuthResend() {
    const {search } = useLocation();
    const navigate = useNavigate();
    const dispatch = useDispatch();

    useEffect(() => {
        const token = new URLSearchParams(search).get("access_token");
        const refreshToken = new URLSearchParams(search).get("refresh_token");


        if (!token) {
            navigate("/login", { replace: true });
            return;
        }

        dispatch(setCredentials({
            access_token: token,
            refresh_token: refreshToken || undefined,
        }));
        dispatch(fetchProfile());
        navigate("/", { replace: true });
    }, [search, dispatch, navigate]);

    return (
        <div className="min-h-screen flex items-center justify-center">
            <p>Подтверждаем email, подождите…</p>
        </div>
    );
}