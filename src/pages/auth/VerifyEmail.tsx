import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { setCredentials } from "@/entities/auth/model/authSlice";

export default function VerifyEmail() {
    const navigate = useNavigate();
    const dispatch = useDispatch();

    useEffect(() => {
        const interval = setInterval(() => {
            const token = localStorage.getItem("access_token");
            const refreshToken = localStorage.getItem("refresh_token");

            if (token) {
                dispatch(setCredentials({
                    access_token: token,
                    refresh_token: refreshToken || undefined,
                }));
                navigate("/", { replace: true });
            }
        }, 2000);

        return () => clearInterval(interval);
    }, [navigate, dispatch]);

    return (
        <div className="min-h-screen flex items-center justify-center">
            <p className="text-lg font-medium">
                Please check your email to verify your account.
            </p>
        </div>
    );
}
