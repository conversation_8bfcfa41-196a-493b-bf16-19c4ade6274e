import React from 'react';
import { IoIosArrowForward } from "react-icons/io";

const userManagementOptions = [
    "User Roles & Permissions",
    "Add/Remove Users",
    "Assign Roles",
    "Set Transaction Limits"
];

const UserManagementPage: React.FC = () => {
    return (
        <div className="p-6">
            <div className="flex flex-col gap-4">
                {userManagementOptions.map((option, index) => (
                    <button key={index} className="w-1/3 px-5 py-4 bg-black text-white rounded-lg flex justify-between items-center cursor-pointer">
                        <span>{option}</span>
                        <IoIosArrowForward size={20} />
                    </button>
                ))}
            </div>
        </div>
    );
};

export default UserManagementPage;
