import React, { useEffect } from 'react';
import TextField from '@/shared/ui/fields/TextField.tsx';
import { IoIosArrowForward } from 'react-icons/io';
import { MdCheckCircle } from 'react-icons/md';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/app/store.ts';
import { FormProvider, useForm } from 'react-hook-form';
import CalendarField from '@/shared/ui/calendar/CalendarField.tsx';
import { updateProfile } from '@/entities/auth/model/profileSlice.ts';
import toast from 'react-hot-toast';

const securityLogin = ['Change password'];

const ProfileSettingsPage: React.FC = () => {
    const profile = useSelector((state: RootState) => state.profile.data);
    const loading = useSelector((state: RootState) => state.profile.loading);
    const dispatch = useDispatch();

    const {
        handleSubmit,
        formState: { isDirty, errors },
        reset,
        ...methods
    } = useForm({
        defaultValues: {
            name: '',
            email: '',
            phone_number: '',
            address: '',
            date_of_birth: '',
        },
        mode: 'onBlur',
        criteriaMode: 'all',
    });

    useEffect(() => {
        if (profile) {
            reset({
                name: profile.name || '',
                email: profile.email || '',
                phone_number: profile.phone_number || '',
                address: profile.address || '',
                date_of_birth:
                    profile.date_of_birth === '0001-01-01T00:00:00Z'
                        ? ''
                        : profile.date_of_birth?.substring(0, 10) || '',
            });
        }
    }, [profile, reset]);

    const onSubmit = async (formData: any) => {
        if (!profile?.company_id) return;

        const result = await dispatch(updateProfile({ ...formData }));

        if (updateProfile.fulfilled.match(result)) {
            toast.success('Профиль обновлён');
            reset(formData); // сбрасываем dirty
        } else {
            toast.error('Ошибка обновления');
        }
    };

    return (
        <div className="p-6">
            <div className="flex flex-col items-center lg:items-start lg:flex-row lg:space-x-10">
                <div className="w-max bg-white h-max rounded-2xl">
                    <div className="py-10 px-18">
                        <div className="flex items-center flex-col">
                            <div className="w-12 h-12 rounded-full bg-orange-400" />
                            <div className="text-center">
                                <p className="text-base font-semibold">Partners</p>
                                <p className="text-sm text-gray-400">{profile?.role}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="w-full lg:w-1/2">
                    <div>
                        <h2 className="text-xl font-bold mb-4 dark:text-white">Profile Details</h2>

                        <FormProvider {...methods}>
                            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                                <TextField
                                    label="Name"
                                    name="name"
                                    error={errors.name?.message}
                                    disabled={loading}
                                />
                                <TextField
                                    label="Email"
                                    name="email"
                                    error={errors.email?.message}
                                    disabled={loading}
                                />
                                <TextField
                                    label="Phone Number"
                                    name="phone_number"
                                    error={errors.phone_number?.message}
                                    disabled={loading}
                                />
                                <TextField
                                    label="Address"
                                    name="address"
                                    error={errors.address?.message}
                                    disabled={loading}
                                />
                                <CalendarField name="date_of_birth" disabled={loading} />

                                {isDirty && (
                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="mt-2 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition"
                                    >
                                        {loading ? 'Saving...' : 'Save Changes'}
                                    </button>
                                )}
                            </form>
                        </FormProvider>
                    </div>

                    <div>
                        <h2 className="text-xl font-bold mt-6 mb-4 dark:text-white">Verification</h2>
                        <div className="space-y-4">
                            <div className="flex justify-between items-center p-4 bg-white rounded-lg">
                                <div>
                                    <p className="text-base font-semibold dark:text-white">Level 1</p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        The basic verification level allows depositing Flat and Crypto; as well as
                                        sending and exchanging Crypto up to $10,000 daily and $50,000 monthly
                                    </p>
                                </div>
                                <div className="flex justify-center w-1/4">
                                    <MdCheckCircle size={24} className="text-purple-600" />
                                </div>
                            </div>
                            <div className="flex justify-between items-center p-4 bg-white rounded-lg">
                                <div>
                                    <p className="text-base font-semibold dark:text-white">Level 2</p>
                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                        Answering some questions increases limits up to $100,000 daily, $500,000 monthly
                                        and lets you withdraw & exchange Flat, Crypto and Forex trading.
                                    </p>
                                </div>
                                <button className="px-4 py-2 bg-yellow-500 rounded-lg cursor-pointer w-1/4">
                                    Get started
                                </button>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h2 className="text-xl font-bold mt-6 mb-4 dark:text-white">Security & Login</h2>
                        <div className="flex flex-col gap-4">
                            {securityLogin.map((item, index) => (
                                <button
                                    key={index}
                                    className="px-5 py-4 bg-black text-white rounded-lg flex justify-between items-center cursor-pointer w-1/2"
                                >
                                    <span>{item}</span>
                                    <IoIosArrowForward size={20} />
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="mt-8 pt-8 flex gap-4 border-t border-m-dark/15">
                        <button className="p-3 bg-m-pink text-white rounded-lg cursor-pointer">
                            Delete Account
                        </button>
                        <button className="p-3 bg-m-pink text-white rounded-lg cursor-pointer">
                            Request Account Closure
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ProfileSettingsPage;
