import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import KycProcess from "@/features/kyc/KycProcess";
import KybProcess from "@/features/kyb/KybProcess";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";

const VerificationFlow = () => {
    const navigate = useNavigate();
    const profile = useSelector((state: RootState) => state.profile.data);

    const kycStatus = profile?.verification_statuses?.kyc_status;
    const kybStatus = profile?.verification_statuses?.kyb_status;

    const isKycVerified = kycStatus === "verified";
    const isKybVerified = kybStatus === "verified";

    const isKycPending = kycStatus === "pending";
    const isKybPending = kybStatus === "pending";

    const isKycRejected = kycStatus === "rejected";
    const isKybRejected = kybStatus === "rejected";

    const isKycNotStarted = !kycStatus || kycStatus === "";
    const isKybNotStarted = !kybStatus || kybStatus === "";

    useEffect(() => {
        if (isKycVerified && isKybVerified) {
            navigate("/");
        }
    }, [isKycVerified, isKybVerified, navigate]);

    let currentStepComponent = null;

    if (isKycNotStarted || isKycRejected) {
        currentStepComponent = <KycProcess />;
    } else if ((isKycVerified || isKycPending) && (isKybNotStarted || isKybRejected)) {
        currentStepComponent = <KybProcess />;
    }


    return (
        <div className="">
            {currentStepComponent}
        </div>
    );
};

export default VerificationFlow;
