import React, { useState } from "react";
import { useClickOutside } from "@/shared/hooks/useClickOutside.ts";

interface Notification {
    id: number;
    message: string;
    time: string;
}

const dummyNotifications: Notification[] = [
    { id: 1, message: "New transaction received", time: "2 min ago" },
    { id: 2, message: "Invoice #1234 paid", time: "10 min ago" },
    { id: 3, message: "KYC verification approved", time: "1 hour ago" },
];

export const NotificationDropdown: React.FC = () => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useClickOutside(() => setIsOpen(false));

    return (
        <div className="relative" ref={dropdownRef}>
            <div
                className={`bg-m-dark p-2 rounded-full cursor-pointer transition-transform ${isOpen ? "rotate-12" : "rotate-0"}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <img src="/images/icons/notification.svg" alt="Notifications" className="transition-transform duration-200" />
            </div>
            {isOpen && (
                <div className="absolute right-0 mt-2 w-72 bg-white dark:bg-m-dark rounded-lg shadow-lg overflow-hidden">
                    <div className="px-4 py-2 border-b bg-m-dark dark:border-gray-700">
                        <h3 className="text-md text-white">Your notifications</h3>
                    </div>
                    <ul className="max-h-60 overflow-auto">
                        {dummyNotifications.length > 0 ? (
                            dummyNotifications.map((notification) => (
                                <li key={notification.id} className="p-3 text-sm border-b dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800">
                                    <p className="dark:text-white">{notification.message}</p>
                                    <span className="text-xs text-gray-500">{notification.time}</span>
                                </li>
                            ))
                        ) : (
                            <li className="p-3 text-lg text-gray-500 dark:text-gray-400 text-center py-10">No notifications</li>
                        )}
                    </ul>
                </div>
            )}
        </div>
    );
};
