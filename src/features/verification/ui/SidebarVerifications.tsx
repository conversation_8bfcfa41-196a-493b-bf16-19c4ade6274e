import { useSelector } from "react-redux";
import { RootState } from "@/app/store";
import { useNavigate } from "react-router-dom";

function SidebarVerifications() {
    const navigate = useNavigate();
    const profile = useSelector((state: RootState) => state.profile.data);

    const kycStatus = profile?.verification_statuses?.kyc_status || "";
    const kybStatus = profile?.verification_statuses?.kyb_status || "";

    const isKycVerified = kycStatus === "verified";
    const isKybVerified = kybStatus === "verified";
    const isKycPending = kycStatus === "pending";
    const isKybPending = kybStatus === "pending";
    const isKycRejected = kycStatus === "rejected";
    const isKybRejected = kybStatus === "rejected";
    const isKycEmpty = !kycStatus || kycStatus === "";
    const isKybEmpty = !kybStatus || kybStatus === "";

    // ✅ Полностью скрыть, если всё верифицировано
    if (isKycVerified && isKybVerified) {
        return null;
    }

    // 💬 Собираем список сообщений
    const messages: string[] = [];

    if (isKycEmpty) messages.push("Please complete KYC");
    if (isKycPending) messages.push("Waiting for KYC approval");
    if (isKycRejected) messages.push("KYC was rejected");

    if (isKybEmpty) messages.push("Please complete KYB");
    if (isKybPending) messages.push("Waiting for KYB approval");
    if (isKybRejected) messages.push("KYB was rejected");

    if (messages.length === 0) messages.push("Account needs verification");

    const hasAnyError = isKycRejected || isKybRejected;

    const totalSteps = 2;
    const completedSteps =
        Number(isKycVerified || isKycPending) + Number(isKybVerified || isKybPending);

    return (
        <div className="px-8 pb-6 cursor-pointer" onClick={() => navigate("/verification")}>
            <div
                className={`p-4 rounded-lg flex justify-between items-start ${
                    hasAnyError ? "bg-red-100 dark:bg-red-900" : "bg-m-dark-muted"
                }`}
            >
                <div className="flex flex-col gap-2">
                    <p
                        className={`text-lg font-semibold ${
                            hasAnyError ? "text-red-600 dark:text-red-400" : "text-white"
                        }`}
                    >
                        {completedSteps}/{totalSteps}
                    </p>
                    <div className="flex flex-col gap-1">
                        {messages.map((msg, idx) => (
                            <p
                                key={idx}
                                className={`text-sm ${
                                    hasAnyError
                                        ? "text-red-600 dark:text-red-400"
                                        : "text-white"
                                }`}
                            >
                                {msg}
                            </p>
                        ))}
                    </div>
                </div>

                <p className="p-1 border rounded-full cursor-pointer bg-white border-white">
                    <img
                        className="rotate-45 w-6 h-6"
                        src="/images/icons/upBalance.svg"
                        alt="icon"
                    />
                </p>
            </div>
        </div>
    );
}

export default SidebarVerifications;
