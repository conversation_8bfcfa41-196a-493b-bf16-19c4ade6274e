import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface VerificationState {
    completedSteps: number;
    totalSteps: number;
    verificationStatus: "Not Started" | "In Progress" | "Verified";
}

const initialState: VerificationState = {
    completedSteps: 0,
    totalSteps: 2, // KYC + KYB
    verificationStatus: "Not Started",
};

const verificationSlice = createSlice({
    name: "verification",
    initialState,
    reducers: {
        completeKYC: (state) => {
            if (state.completedSteps === 0) { // Убеждаемся, что KYC не повторяется
                state.completedSteps += 1;
                state.verificationStatus = "In Progress";
            }
        },
        completeKYB: (state) => {
            if (state.completedSteps === 1) { // KYB идет после KYC
                state.completedSteps += 1;
                state.verificationStatus = "Verified"; // После KYB все завершено
            }
        },
        resetVerification: (state) => {
            state.completedSteps = 0;
            state.verificationStatus = "Not Started";
        }
    }
});

export const { completeKYC, completeKYB, resetVerification } = verificationSlice.actions;
export default verificationSlice.reducer;
