import React, { useState } from "react";
import {useClickOutside} from "@/shared/hooks/useClickOutside.ts";

const actions = [
    "Deposit",
    "Send",
    "Exchange",
    "Scan&Pay",
    "Batch Payment",
    "New Account"
];

const QuickActions: React.FC = () => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useClickOutside(() => setIsOpen(false));

    return (
        <div className="relative" ref={dropdownRef}>
            {/* Кнопка-триггер */}
            <div
                className={`bg-m-dark p-2 rounded-full cursor-pointer transition-transform duration-200 ${isOpen ? "rotate-180" : "rotate-0"}`}
                onClick={() => setIsOpen(!isOpen)}
            >
                <img src="/images/icons/light.svg" alt="Quick Actions" />
            </div>

            {/* Выпадающий список */}
            {isOpen && (
                <div className="absolute right-0 mt-2 w-40 bg-m-dark text-white rounded-lg shadow-lg overflow-hidden p-2">
                    <ul>
                        {actions.map((action) => (
                            <li
                                key={action}
                                className="p-2 hover:bg-gray-700 cursor-pointer rounded-lg"
                                onClick={() => setIsOpen(false)}
                            >
                                {action}
                            </li>
                        ))}
                    </ul>
                </div>
            )}
        </div>
    );
};

export default QuickActions;