import React from 'react';
import { FiFilter, FiX } from 'react-icons/fi';

interface FilterProps {
    filters: {
        status: string;
        type: string;
        dateRange: string;
        category: string;
    };
    onFilterChange: (filters: FilterProps['filters']) => void;
}

const TransactionFilters: React.FC<FilterProps> = ({ filters, onFilterChange }) => {
    const handleFilterChange = (key: keyof FilterProps['filters'], value: string) => {
        onFilterChange({
            ...filters,
            [key]: value
        });
    };

    const clearFilters = () => {
        onFilterChange({
            status: 'all',
            type: 'all',
            dateRange: 'all',
            category: 'all'
        });
    };

    const hasActiveFilters = Object.values(filters).some(value => value !== 'all');

    return (
        <div className="flex flex-wrap items-center gap-3">
            {/* Status Filter */}
            <div className="relative">
                <select
                    value={filters.status}
                    onChange={(e) => handleFilterChange('status', e.target.value)}
                    className="appearance-none bg-white dark:bg-m-dark border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm dark:text-white focus:outline-none focus:ring-2 focus:ring-m-pink focus:border-transparent"
                >
                    <option value="all">All Status</option>
                    <option value="completed">Completed</option>
                    <option value="pending">Pending</option>
                    <option value="failed">Failed</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Type Filter */}
            <div className="relative">
                <select
                    value={filters.type}
                    onChange={(e) => handleFilterChange('type', e.target.value)}
                    className="appearance-none bg-white dark:bg-m-dark border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm dark:text-white focus:outline-none focus:ring-2 focus:ring-m-pink focus:border-transparent"
                >
                    <option value="all">All Types</option>
                    <option value="credit">Credit</option>
                    <option value="debit">Debit</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Date Range Filter */}
            <div className="relative">
                <select
                    value={filters.dateRange}
                    onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                    className="appearance-none bg-white dark:bg-m-dark border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm dark:text-white focus:outline-none focus:ring-2 focus:ring-m-pink focus:border-transparent"
                >
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="yesterday">Yesterday</option>
                    <option value="last7days">Last 7 Days</option>
                    <option value="last30days">Last 30 Days</option>
                    <option value="last90days">Last 90 Days</option>
                    <option value="thisMonth">This Month</option>
                    <option value="lastMonth">Last Month</option>
                    <option value="thisYear">This Year</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Category Filter */}
            <div className="relative">
                <select
                    value={filters.category}
                    onChange={(e) => handleFilterChange('category', e.target.value)}
                    className="appearance-none bg-white dark:bg-m-dark border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm dark:text-white focus:outline-none focus:ring-2 focus:ring-m-pink focus:border-transparent"
                >
                    <option value="all">All Categories</option>
                    <option value="Food & Dining">Food & Dining</option>
                    <option value="Shopping">Shopping</option>
                    <option value="Transfer">Transfer</option>
                    <option value="Cash Withdrawal">Cash Withdrawal</option>
                    <option value="Salary">Salary</option>
                    <option value="Bills & Utilities">Bills & Utilities</option>
                    <option value="Entertainment">Entertainment</option>
                    <option value="Transportation">Transportation</option>
                    <option value="Healthcare">Healthcare</option>
                    <option value="Investment">Investment</option>
                    <option value="Other">Other</option>
                </select>
                <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                </div>
            </div>

            {/* Filter Icon */}
            <div className="flex items-center gap-2">
                <FiFilter className="text-gray-500 dark:text-gray-400" size={16} />
                
                {/* Clear Filters Button */}
                {hasActiveFilters && (
                    <button
                        onClick={clearFilters}
                        className="flex items-center gap-1 px-3 py-2 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                        title="Clear all filters"
                    >
                        <FiX size={14} />
                        Clear
                    </button>
                )}
            </div>

            {/* Active Filters Display */}
            {hasActiveFilters && (
                <div className="flex flex-wrap gap-2 ml-2">
                    {filters.status !== 'all' && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-m-pink/10 text-m-pink text-xs rounded-full">
                            Status: {filters.status}
                            <button
                                onClick={() => handleFilterChange('status', 'all')}
                                className="hover:text-m-pink/80"
                            >
                                <FiX size={12} />
                            </button>
                        </span>
                    )}
                    {filters.type !== 'all' && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-m-pink/10 text-m-pink text-xs rounded-full">
                            Type: {filters.type}
                            <button
                                onClick={() => handleFilterChange('type', 'all')}
                                className="hover:text-m-pink/80"
                            >
                                <FiX size={12} />
                            </button>
                        </span>
                    )}
                    {filters.dateRange !== 'all' && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-m-pink/10 text-m-pink text-xs rounded-full">
                            Date: {filters.dateRange}
                            <button
                                onClick={() => handleFilterChange('dateRange', 'all')}
                                className="hover:text-m-pink/80"
                            >
                                <FiX size={12} />
                            </button>
                        </span>
                    )}
                    {filters.category !== 'all' && (
                        <span className="inline-flex items-center gap-1 px-2 py-1 bg-m-pink/10 text-m-pink text-xs rounded-full">
                            Category: {filters.category}
                            <button
                                onClick={() => handleFilterChange('category', 'all')}
                                className="hover:text-m-pink/80"
                            >
                                <FiX size={12} />
                            </button>
                        </span>
                    )}
                </div>
            )}
        </div>
    );
};

export default TransactionFilters;
