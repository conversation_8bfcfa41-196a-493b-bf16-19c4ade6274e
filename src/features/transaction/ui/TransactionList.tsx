import React from "react";

interface Transaction {
    id: number;
    name: string;
    amount: string;
    date: string;
    icons: string;
}

interface TransactionListProps {
    transactions: Transaction[];
    title?: string;
}

export const TransactionList: React.FC<TransactionListProps> = ({ transactions, title = "Transactions" }) => {
    return (
        <div className="h-full p-4 rounded-lg shadow bg-white dark:bg-m-dark">
            <div className="flex items-center justify-between w-full">
                <h2 className="text-lg font-medium dark:text-white">{title}</h2>
                <p className='p-2 border rounded-full cursor-pointer dark:border-white'>
                    <img className='rotate-45 dark:invert' src='/images/icons/upBalance.svg' alt='icons' />
                </p>
            </div>
            <ul className="mt-4 space-y-7 overflow-y-auto max-h-[560px] pr-2">
                {transactions.map((tx) => (
                    <li key={tx.id} className="flex justify-between items-center">
                        <div className="flex items-center gap-3">
                            <div className="p-2 rounded-xl bg-m-pink/25">
                                <img src={tx.icons} alt='icons' />
                            </div>
                            <div className="flex flex-col dark:text-white">
                                <span>{tx.name}</span>
                                <span className='text-sm text-m-dark/65 dark:text-white/65'>{tx.date}</span>
                            </div>
                        </div>
                        <span className="dark:text-white">{tx.amount}</span>
                    </li>
                ))}
            </ul>
        </div>
    );
};
