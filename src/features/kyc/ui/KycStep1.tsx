import {Form<PERSON>rovider, useForm} from "react-hook-form";
import { useDispatch } from "react-redux";
import { updateData, setFile } from "@/features/kyc/model/kycSlice";
import TextField from "@/shared/ui/fields/TextField.tsx";
import CountrySelect from "@/shared/ui/fields/CountrySelectField.tsx";
import FileUpload from "@/shared/ui/fileUpload";
import {useEffect, useState} from "react";
import CalendarField from "@/shared/ui/calendar/CalendarField.tsx";

const KycStep1 = ({ onNext }: { onNext: () => void }) => {
    const dispatch = useDispatch();
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        clearErrors,
        setError,
        formState: { errors },
    } = useForm();
    const methods = useForm();


    const [file1, setFile1] = useState<File | null>(null);
    const [file2, setFile2] = useState<File | null>(null);
    const [fileError, setFileError] = useState<string | null>(null);



    const onSubmit = (data: any) => {
        let valid = true;

        if (!file1) {
            setError("file1", { type: "manual", message: "File is required" });
            valid = false;
        }
        if (!file2) {
            setError("file2", { type: "manual", message: "File is required" });
            valid = false;
        }

        if (!valid) return;

        // Объединяем файлы с формой
        dispatch(updateData(data));
        dispatch(setFile({ key: "file1", file: file1 }));
        dispatch(setFile({ key: "file2", file: file2 }));
        onNext();
    };


    useEffect(() => {
        register("file1", { required: "File is required" });
        register("file2", { required: "File is required" });
    }, [register]);

    return (

        <FormProvider {...methods}>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-full xl:w-1/2">
                <h2 className="text-xl font-bold text-center dark:text-white">Company Data</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <TextField
                        label="Company Name"
                        {...register("companyName", { required: "Company name is required" })}
                        error={errors.companyName?.message}
                    />
                    <CountrySelect
                        label="Country"
                        name="country"
                        {...register("country", { required: "Сountry name is required" })}
                        error={errors.country?.message}
                        setValue={(name, value) => {
                            setValue(name, value);
                            clearErrors(name); // Сбрасываем ошибку при выборе страны
                        }}
                        watch={watch}
                    />
                    <TextField
                        label="Registration Number"
                        {...register("registrationNumber", { required: "Registration number is required" })}
                        error={errors.registrationNumber?.message}
                    />

                    <TextField
                        label="Type of entity"
                        {...register("typeOfEntity", { required: "Entity is required" })}
                        error={errors.typeOfEntity?.message}
                    />
                    <TextField
                        label="Legal address"
                        {...register("legalAddress", { required: "Address is required" })}
                        error={errors.legalAddress?.message}
                    />
                    <TextField
                        label="City"
                        {...register("city", { required: "City is required" })}
                        error={errors.city?.message}
                    />
                    <TextField
                        label="Website"
                        {...register("website", { required: "Website ID is required" })}
                        error={errors.website?.message}
                    />
                    <TextField
                        label="Tax ID"
                        {...register("taxId", { required: "Tax ID is required" })}
                        error={errors.taxId?.message}
                    />
                    <TextField
                        label="Company email"
                        name="companyEmail"
                        error={errors.companyEmail?.message}
                        {...register("companyEmail", { required: "Email is required", pattern: { value: /\S+@\S+\.\S+/, message: "Invalid email address" } })}
                    />
                    <TextField
                        label="Company telephone"
                        {...register("companyTelephone", { required: "Company telephone is required" })}
                        error={errors.companyTelephone?.message}
                    />
                    <CalendarField name="date_of_birth"/>
                </div>

                <div className="flex flex-col gap-4">
                    <span className='dark:text-white'>Upload the following:</span>
                    <div className='bg-m-orange p-4 rounded-xl'>
                        <ul className='dark:text-white/65'>
                            <li>Document confirming the company’s legal existence (e.g, the certificate of incorporation or a recent excerpt from a state company registry)</li>
                        </ul>
                    </div>
                </div>
                <div className='flex gap-3'>
                    <FileUpload
                        label="Document 1"
                        onFileSelect={(file) => {
                            setFile1(file);
                            setValue("file1", file);
                            clearErrors("file1");
                        }}
                        error={errors.file1?.message}
                    />
                    <FileUpload
                        label="Document 2"
                        onFileSelect={(file) => {
                            setFile2(file);
                            setValue("file2", file);
                            clearErrors("file2");
                        }}
                        error={errors.file2?.message}
                    />
                </div>
                {fileError && <p className="text-red-500 text-xs mt-1">{fileError}</p>}
                <button type="submit" className="bg-m-dark text-white p-2 rounded-xl w-full cursor-pointer dark:bg-m-orange dark:text-m-dark">Next</button>
            </form>
        </FormProvider>
    );
};

export default KycStep1;
