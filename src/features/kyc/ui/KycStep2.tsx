import { useForm } from "react-hook-form";
import {useDispatch, useSelector} from "react-redux";
import {resetKyc, updateData} from "@/features/kyc/model/kycSlice";
import { completeKYC } from "@/features/verification/model/verificationSlice";
import TextField from "@/shared/ui/fields/TextField.tsx";
import {useNavigate} from "react-router-dom";
import {RootState} from "@/app/store.ts";
import {sendKycData} from "@/features/kyc/api/kycApi.ts";
import {fetchProfile} from "@/entities/auth/model/profileSlice.ts";

const KycStep2 = ({ onBack }: { onBack: () => void }) => {
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const kycState = useSelector((state: RootState) => state.kyc);
    const profile = useSelector((state: RootState) => state.profile.data);


    const companyId = profile?.company_id


    const {
        register,
        handleSubmit,
        watch,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm();

    const onSubmit = async (formData: any) => {
        const mergedData = {
            ...kycState.data,
            ...formData,
        };

        if (!companyId) {
            console.error("❌ companyId отсутствует");
            return;
        }

        const success = await sendKycData(mergedData, kycState.files, companyId);

        console.log(success, 'success');

        if (success) {
            dispatch(updateData(formData));
            dispatch(completeKYC());
            dispatch(fetchProfile());
            dispatch(resetKyc());
            navigate("/verification");
        } else {
            console.error("❌ Ошибка при отправке KYC");
        }
    };



    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-1/2">
            <h2 className="text-xl font-bold text-center mb-8 dark:text-white">Representatives</h2>
            <div className="grid grid-cols-2 gap-4 border-b border-m-dark/15 dark:border-white/15 pb-8">
                <TextField
                    label="First Name"
                    {...register("firstName", { required: "First name is required" })}
                    error={errors.firstName?.message}
                />
                <TextField
                    label="Last Name"
                    {...register("lastName", { required: "Last name is required" })}
                    error={errors.firstName?.message}
                />
                <TextField
                    label="Email"
                    {...register("email", { required: "Email is required", pattern: { value: /\S+@\S+\.\S+/, message: "Invalid email address" } })}
                    error={errors.email?.message}
                />
                <TextField
                    label="Contact number"
                    {...register("contactNumber", { required: "Contact number is required" })}
                    error={errors.contactNumber?.message}
                />
                <TextField
                    label="Position"
                    {...register("position", { required: "Position is required" })}
                    error={errors.position?.message}
                />
            </div>
            {/*<div className="flex flex-col items-center p-8 bg-m-orange text-black rounded-xl my-8">*/}
            {/*   <p className="flex flex-col text-center space-y-2 dark:text-white">*/}
            {/*       <span className='font-semibold text-lg'>Identity verification</span>*/}
            {/*       <span className='text-base text-m-dark/65'>This applicant has to go through identity check</span>*/}
            {/*   </p>*/}
            {/*    <button type="button" className="bg-white dark:bg-m-dark dark:text-white p-2 rounded-xl mt-10 w-max cursor-pointer">Start verification now</button>*/}
            {/*</div>*/}

            <div className="flex space-x-2">
                <button type="button" onClick={onBack} className="border dark:border-white/15 p-2 rounded-xl w-1/2 cursor-pointer dark:text-white">Previous</button>
                <button type="submit" className="bg-m-dark dark:bg-m-orange dark:text-m-dark text-white p-2 rounded-xl w-1/2 cursor-pointer">Complete</button>
            </div>
        </form>
    );
};

export default KycStep2;
