import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/store";
import { nextStep, prevStep } from "@/features/kyc/model/kycSlice";
import KycStep1 from "@/features/kyc/ui/KycStep1.tsx";
import KycStep2 from "@/features/kyc/ui/KycStep2.tsx";
import {useNavigate} from "react-router-dom";
import {useEffect} from "react";

const KycProcess = () => {
    const step = useSelector((state: RootState) => state.kyc.step);
    const dispatch = useDispatch();

    const steps = [
        { id: 1, label: "Step 1" },
        { id: 2, label: "Step 2" },
    ];

    return (
        <div className="px-4">
            <div className='flex gap-3'>
                {steps.map(({ id, label }, index) => (
                    <div className="flex flex-col w-[200px]" key={index}>
                        <div className={`w-full h-1 bg-gray-300 ${step > id ? "bg-gradient-to-r from-[#D46076] to-[#5E2FEC]" : ""}`}></div>

                        <div className="flex items-center gap-3 mt-4">
                            <div
                                className={`flex items-center justify-center w-7 h-7 rounded-full border-2 transition ${
                                    step >= id ? "bg-m-violet border-m-violet text-white" : "border-gray-300  text-gray-500"
                                }`}
                            >
                                {step > id ? (
                                    <span className="text-lg font-bold">✔</span>
                                ) : (
                                    <span></span>
                                )}
                            </div>
                            <span className='dark:text-white'>{label}</span>
                        </div>
                    </div>
                ))}
            </div>

            {step === 1 ? (
                <div className="flex justify-center ">
                    <KycStep1 onNext={() => dispatch(nextStep())} />
                </div>
            ) : (
                <div className="flex justify-center ">
                    <KycStep2 onBack={() => dispatch(prevStep())} />
                </div>
            )}
        </div>
    );
};


export default KycProcess;
