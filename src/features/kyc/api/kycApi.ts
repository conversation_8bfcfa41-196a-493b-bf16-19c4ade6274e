import axiosInstance from "@/shared/api/axiosInstance.ts";

export const sendKycData = async (
    data: Record<string, any>,
    files: { file1: File | null; file2: File | null },
    companyId: string
): Promise<boolean> => {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            formData.append(key, String(value));
        }
    });

    if (files.file1) formData.append("document1", files.file1);
    if (files.file2) formData.append("document2", files.file2);

    try {
        await axiosInstance.post(`/api/v1/kyc/verify/${companyId}`, formData, {
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });

        return true;
    } catch (error) {
        console.error("KYC API error", error);
        return false;
    }
};
