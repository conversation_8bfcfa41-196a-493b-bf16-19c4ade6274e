import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface KycFormData {
    companyName: string;
    country: string;
    registrationNumber: string;
    typeOfEntity: string;
    legalAddress: string;
    city: string;
    website: string;
    taxId: string;
    companyEmail: string;
    companyTelephone: string;
    date: string;
    firstName: string;
    lastName: string;
    email: string;
    contactNumber: string;
    position: string;
}



interface KycState {
    step: number;
    data: KycFormData;
    files: {
        file1: File | null;
        file2: File | null;
    };
}


const initialState: KycState = {
    step: 1,
    data: {},
    files: {
        file1: null,
        file2: null,
    }
};

const kycSlice = createSlice({
    name: 'kyc',
    initialState,
    reducers: {
        nextStep: (state) => {
            state.step += 1;
        },
        prevStep: (state) => {
            state.step -= 1;
        },
        setFile: (state, action: PayloadAction<{ key: "file1" | "file2"; file: File }>) => {
            state.files[action.payload.key] = action.payload.file;
        },
        updateData: (state, action: PayloadAction<Record<string, any>>) => {
            state.data = { ...state.data, ...action.payload };
        },
        resetKyc: () => initialState, // Обнуляем данные после завершения
    }
});

export const { nextStep, prevStep,setFile, updateData, resetKyc } = kycSlice.actions;
export default kycSlice.reducer;
