import { useEffect, useState } from "react";

const ThemeSwitcher: React.FC = () => {
    const [isDarkMode, setIsDarkMode] = useState(() => {
        return localStorage.getItem("theme") === "dark";
    });

    useEffect(() => {
        if (isDarkMode) {
            document.documentElement.classList.add("dark");
            localStorage.setItem("theme", "dark");
        } else {
            document.documentElement.classList.remove("dark");
            localStorage.setItem("theme", "light");
        }
    }, [isDarkMode]);

    return (
        <button
            className="bg-m-dark p-2 rounded-full cursor-pointer"
            onClick={() => setIsDarkMode(!isDarkMode)}
        >
            <img
                src={isDarkMode ? "/images/icons/moon.svg" : "/images/icons/sun.svg"}
                alt={isDarkMode ? "Light Mode" : "Dark Mode"}
            />
        </button>
    );
};

export default ThemeSwitcher;
