import axiosInstance from "@/shared/api/axiosInstance.ts";

export const sendKybData = async (
    data: Record<string, any>,
    files: Record<string, File | null>,
    companyId: string
): Promise<boolean> => {
    const formData = new FormData();

    Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
            formData.append(key, String(value));
        }
    });

    Object.entries(files).forEach(([key, file]) => {
        if (file) {
            formData.append(key, file);
        }
    });

    try {
        await axiosInstance.post(`/api/v1/kyb/verify/${companyId}`, formData);
        return true;
    } catch (error: any) {
        console.error("KYC API error", error);
        return false;
    }
};
