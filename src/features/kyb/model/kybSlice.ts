import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface KybState {
    step: number;
    data: Record<string, any>;
}

const initialState: KybState = {
    step: 1,
    data: {}
};

const kybSlice = createSlice({
    name: 'kyb',
    initialState,
    reducers: {
        nextStep: (state) => {
            state.step += 1;
        },
        prevStep: (state) => {
            state.step -= 1;
        },
        updateData: (state, action: PayloadAction<Partial<KybState['data']>>) => {
            state.data = { ...state.data, ...action.payload };
        },
        resetKyb: () => initialState,
    }
});

export const { nextStep, prevStep, updateData, resetKyb } = kybSlice.actions;
export default kybSlice.reducer;
