import React, { useState } from "react";
import {useDispatch, useSelector} from "react-redux";
import { useForm } from "react-hook-form";
// import {resetKyb, updateData} from "@/features/kyb/model/kybSlice.ts";
import {completeKYB} from "@/features/verification/model/verificationSlice.ts";
import KybCompletionModal from "@/features/kyb/ui/KybCompletionModal.tsx";
import {useNavigate} from "react-router-dom";
import {RootState} from "@/app/store.ts";
import {sendKybData} from "@/features/kyb/api/kybApi.ts";
import {fetchProfile} from "@/entities/auth/model/profileSlice.ts";

const KybStep3 = ({ onBack, onNext }: { onBack: () => void; onNext: () => void }) => {
    const dispatch = useDispatch();
    const data = useSelector((state: RootState) => state.kyb.data);
    const profile = useSelector((state: RootState) => state.profile.data);


    const companyId = profile?.company_id
    console.log(data, 'data')
    const navigate = useNavigate();
    const { handleSubmit } = useForm();
    const [isModalOpen, setIsModalOpen] = useState(false);


    const [files, setFiles] = useState<{ [key: string]: File | null }>({
        financialStatements: null,
        organizationChart: null,
        tradingBankStatements: null,
    });


    const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
        if (event.target.files && event.target.files.length > 0) {
            setFiles((prevFiles) => ({
                ...prevFiles,
                [fieldName]: event.target.files![0],
            }));
        }
    };

    const onSubmit = async () => {

        const success = await sendKybData(data, files, companyId);

        if (success) {
            dispatch(completeKYB());
            dispatch(fetchProfile());
            setIsModalOpen(true);
        } else {
            // обработка ошибки (модалка или тостер)
        }
    };



    const handleContinue = () => {
        setIsModalOpen(false);
        navigate("/"); // Перенаправляем на главную страницу
        setTimeout(() => {
            window.open("https://sumsub.com/", "_blank");
        }, 500); // Открываем Sumsub после небольшой задержки
    };

    return (
       <>
           <form onSubmit={handleSubmit(onSubmit)} className="space-y-6 w-full max-w-2xl">
               <h2 className="text-xl font-bold mb-8 text-gray-700 dark:text-white">
                   3. Additional Company Documents
               </h2>

               {/* Файлы */}
               <div className="grid grid-cols-1 gap-4 border-b border-m-dark/15 dark:border-white/15 pb-8">
                   {[
                       { label: "Financial statements (last 12 months)", name: "financialStatements" },
                       { label: "Organization Chart", name: "organizationChart" },
                       { label: "Trading Bank Statements", name: "tradingBankStatements" },
                   ].map(({ label, name }) => (
                       <div key={name} className="relative border rounded-lg p-3 flex items-center justify-between bg-white dark:bg-gray-900">
                           <span className="text-gray-500">{files[name] ? files[name]!.name : label}</span>
                           <label className="cursor-pointer">
                               <input
                                   type="file"
                                   className="hidden"
                                   onChange={(event) => handleFileUpload(event, name)}
                               />
                               <img src='/images/icons/uploadKyb.svg' alt='icon'/>
                           </label>
                       </div>
                   ))}
               </div>
               <div className="flex space-x-2">
                   <button
                       type="button"
                       onClick={onBack}
                       className="border px-4 py-2 rounded-lg text-gray-700 dark:text-white w-1/2 cursor-pointer"
                   >
                       Previous
                   </button>
                   <button
                       type="submit"
                       className="bg-black text-white px-6 py-2 rounded-lg w-1/2 cursor-pointer"
                   >
                       Submit
                   </button>
               </div>
           </form>
           <KybCompletionModal
               isOpen={isModalOpen}
               onClose={() => setIsModalOpen(false)}
               onContinue={handleContinue}
           />
       </>
    );
};

export default KybStep3;
