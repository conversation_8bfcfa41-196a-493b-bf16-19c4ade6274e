import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import {updateData} from "@/features/kyb/model/kybSlice.ts";
import TextField from "@/shared/ui/fields/TextField.tsx";
import {useState} from "react";
import SelectField from "@/shared/ui/fields/SelectField.tsx";



const businessActivityOptions = [
    { value: "agriculture", label: "Agriculture & Natural Resources" },
    { value: "manufacturing", label: "Manufacturing & Production" },
    { value: "retail", label: "Retail & Wholesale" },
    { value: "financial", label: "Financial Services" },
    { value: "professional", label: "Professional Services" },
    { value: "it", label: "Information Technology" },
    { value: "telecom", label: "Telecommunications" },
    { value: "healthcare", label: "Healthcare & Pharmaceuticals" },
    { value: "transportation", label: "Transportation & Logistics" },
    { value: "hospitality", label: "Hospitality & Entertainment" },
    { value: "education", label: "Education & Research" },
    { value: "government", label: "Government & Non-Profit" },
    { value: "energy", label: "Energy & Utilities" },
    { value: "others", label: "Others (Specify)" },
];



const KybStep1 = ({ onNext }: { onNext: () => void }) => {
    const dispatch = useDispatch();
    const [showOtherField, setShowOtherField] = useState(false);
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm();


    const onSubmit = (data: any) => {
        dispatch(updateData(data));
        onNext();
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-1/2">
            <h2 className="text-xl font-bold dark:text-white">1. Business Information</h2>
           <div className="grid grid-cols-1 gap-4">
               <TextField
                   label="Business address"
                   {...register("business_address", { required: "Business address name is required" })}
                   error={errors.business_address?.message}
               />
               <TextField
                   label="Company trading name"
                   {...register("company_trading_name", { required: "Company trading name is required" })}
                   error={errors.company_trading_name?.message}
               />

               <SelectField
                   label="Business activity"
                   name="business_activity"
                   options={businessActivityOptions}
                   error={errors.business_activity?.message}
                   {...register("business_activity", { required: "Business activity is required" })}
                   onChange={(e) => {
                       setValue("business_activity", e.target.value);
                       clearErrors("business_activity");
                       register("business_activity").onChange(e);
                   }}


               />
               <TextField
                   label="Who are your business partners?"
                   {...register("business_partners", { required: "Business partners is required" })}
                   error={errors.business_partners?.message}
               />

               <TextField
                   label="Business website"
                   {...register("business_website", { required: "Business website is required" })}
                   error={errors.business_website?.message}
               />
           </div>

            <button type="submit" className="bg-m-dark text-white p-2 rounded-xl w-full cursor-pointer dark:bg-m-orange dark:text-m-dark">Next</button>
        </form>
    );
};

export default KybStep1;
