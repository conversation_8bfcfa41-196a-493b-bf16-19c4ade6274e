import { useForm } from "react-hook-form";
import { useDispatch } from "react-redux";
import {updateData} from "@/features/kyb/model/kybSlice.ts";
import TextField from "@/shared/ui/fields/TextField.tsx";
import CountrySelect from "@/shared/ui/fields/CountrySelectField.tsx";

const KybStep2 = ({ onBack, onNext }: { onBack: () => void; onNext: () => void }) => {
    const dispatch = useDispatch();
    const {
        register,
        handleSubmit,
        watch,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm();

    const onSubmit = (data: any) => {
        dispatch(updateData(data));
        onNext();
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 w-1/2">
            <h2 className="text-xl font-bold mb-8 dark:text-white">2. Finance details</h2>
            <div className="grid grid-cols-1 gap-4 border-b border-m-dark/15 dark:border-white/15 pb-8">
                <TextField
                    label="Purpose of use of the payment account"
                    {...register("purpose", { required: "Payment account name is required" })}
                    error={errors.purpose?.message}
                />
                <TextField
                    label="Target business sectors"
                    {...register("sectors", { required: "Business sector name is required" })}
                    error={errors.sectors?.message}
                />

                <CountrySelect
                    label="Countries from which payment will be processed"
                    name="countryFromProcessed"
                    {...register("payment_from_countries", { required: "Сountry is required" })}
                    error={errors.payment_from_countries?.message}
                    setValue={(name, value) => {
                        setValue(name, value);
                        clearErrors(name);
                    }}
                    watch={watch}
                />
                <CountrySelect
                    label="Countries to which payment will be processed"
                    name="countryToProcessed"
                    {...register("payment_to_countries", { required: "Сountry is required" })}
                    error={errors.payment_to_countries?.message}
                    setValue={(name, value) => {
                        setValue(name, value);
                        clearErrors(name);
                    }}
                    watch={watch}
                />
                <TextField
                    label="Expected incoming transactions volume in EUR on a monthly basis"
                    {...register("income_eur", { required: "Transactions is required" })}
                    error={errors.income_eur?.message}
                />
                <TextField
                    label="Expected incoming transactions number on a monthly basis"
                    {...register("tx_count", { required: "Transactions is required" })}
                    error={errors.tx_count?.message}
                />
                <TextField
                    label="Expected average amount of single incoming transaction in EUR"
                    {...register("avg_tx_amount", { required: "Single transaction is required" })}
                    error={errors.avg_tx_amount?.message}
                />
                <TextField
                    label="Expected outgoing transactions number on a monthly basis"
                    {...register("outgoing_tx_count", { required: "Transactions is required" })}
                    error={errors.outgoing_tx_count?.message}
                />
                <TextField
                    label="Expected average amount of single outgoing transaction in EUR"
                    {...register("avg_outgoing_tx_amount", { required: "Transactions is required" })}
                    error={errors.avg_outgoing_tx_amount?.message}
                />
            </div>


            {/* Navigation Buttons */}
            <div className="flex space-x-2">
                <button
                    type="button"
                    onClick={onBack}
                    className="border dark:border-white/15 p-2 rounded-xl w-1/2 cursor-pointer dark:text-white"
                >
                    Previous
                </button>
                <button
                    type="submit"
                    className="bg-m-dark dark:bg-m-orange dark:text-m-dark text-white p-2 rounded-xl w-1/2 cursor-pointer"
                >
                    Next
                </button>
            </div>
        </form>
    );
};

export default KybStep2;
