
const KybCompletionModal = ({ isOpen, onClose, onContinue }: { isOpen: boolean; onClose: () => void; onContinue: () => void }) => {
    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50">
            <div className="bg-white p-6 rounded-lg shadow-lg w-[400px] text-center">
                <h2 className="text-lg font-semibold">
                    The KYB verification process has been successfully completed.
                </h2>
                <p className="text-gray-600 mt-2">
                    Please proceed with Sumsup verification as the next step.
                </p>

                <div className="border-t border-m-dark/15 my-4"></div>

                <p className="text-sm text-gray-500">Let's get you verified</p>

                <a
                    href="https://sumsub.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block bg-black text-white px-6 py-2 rounded-lg w-full mt-4 cursor-pointer"
                    onClick={onContinue}
                >
                    Continue
                </a>
            </div>
        </div>
    );
};

export default KybCompletionModal;
