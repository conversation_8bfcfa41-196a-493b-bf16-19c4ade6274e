import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/app/store";
import KybStep1 from "@/features/kyb/ui/KybStep1.tsx";
import KybStep2 from "@/features/kyb/ui/KybStep2.tsx";
import KybStep3 from "@/features/kyb/ui/KybStep3.tsx";
import {nextStep, prevStep} from "@/features/kyb/model/kybSlice.ts";

const KybProcess = () => {
    const step = useSelector((state: RootState) => state.kyb.step);
    const dispatch = useDispatch();

    const steps = [
        { id: 1, label: "Business Information" },
        { id: 2, label: "Finance Details" },
        { id: 3, label: "Additional Company Documents" }
    ];


    return (
        <div className="px-4">
            <div className="flex space-x-4 my-4">
                <div className='flex gap-3'>
                    {steps.map(({ id, label }, index) => (
                        <div className="flex flex-col w-[300px]" key={index}>
                            <div className={`w-full h-1 bg-gray-300 ${step > id ? "bg-gradient-to-r from-[#D46076] to-[#5E2FEC]" : ""}`}></div>

                            <div className="flex items-center gap-3 mt-4">
                               <div
                                   className={`flex items-center justify-center w-7 h-7 rounded-full border-2 transition ${
                                       step >= id ? "bg-m-violet border-m-violet text-white" : "border-gray-300  text-gray-500"
                                   }`}
                               >
                                   {step > id ? (
                                       <span className="text-lg font-bold">✔</span>
                                   ) : (
                                       <span></span>
                                   )}
                               </div>
                               <span className='dark:text-white'>{label}</span>
                           </div>
                            
                        </div>

                    ))}
                </div>

                {/*<span className={`p-2 ${step === 1 ? "font-bold" : "text-gray-400"}`}>Business Information</span>*/}
                {/*<span className={`p-2 ${step === 2 ? "font-bold" : "text-gray-400"}`}>Finance Details</span>*/}
                {/*<span className={`p-2 ${step === 3 ? "font-bold" : "text-gray-400"}`}>Additional Company Documents</span>*/}
            </div>

            {step === 1 && (
                <div className="flex justify-center ">
                    <KybStep1 onNext={() => dispatch(nextStep())} />
                </div>
            )}
            {step === 2 && (
                <div className="flex justify-center ">
                    <KybStep2
                        onNext={() => dispatch(nextStep())}
                        onBack={() => dispatch(prevStep())}
                    />
                </div>
            )}
            {step === 3 && (
                <div className="flex justify-center ">
                    <KybStep3 onBack={() => dispatch(prevStep())} />
                </div>
            )}
        </div>
    );
};

export default KybProcess;
