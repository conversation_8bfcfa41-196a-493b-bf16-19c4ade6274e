import TextField from "@/shared/ui/fields/TextField.tsx";
import Pass<PERSON><PERSON>ield from "@/shared/ui/fields/PasswordField.tsx";
import {useForm} from "react-hook-form";
import {useDispatch} from "react-redux";
import {useNavigate} from "react-router-dom";
import {login} from "@/entities/auth/model/authSlice.ts";

interface LoginForm {
    Email: string;
    Password: string;
}

function LoginForm() {
    const {register, handleSubmit, formState: { errors },} = useForm<LoginForm>();
    const dispatch = useDispatch();
    const navigate = useNavigate();

    const onSubmit = async (data: LoginForm) => {
        try {
            await dispatch(login(data)).unwrap();
            navigate("/");
        } catch (error) {
            console.log(error);
        }
    };


    console.log("LoginForm render");


    return (
        <form onSubmit={handleSubmit(onSubmit)} className="w-full max-w-lg p-6 space-y-4">
            <h1 className="text-center text-xl font-semibold pb-4 border-b border-m-dark/15">
                Sign in
            </h1>
            <TextField
                label="Email"
                name="Email"
                error={errors.Email?.message}
                {...register("Email", { required: "Email is required", pattern: { value: /\S+@\S+\.\S+/, message: "Invalid email address" } })}
            />
            <PasswordField
                label="Password"
                name="Password"
                error={errors.Password?.message}
                {...register("Password",{
                    required: "Password is required",
                    minLength: { value: 6, message: "Minimum 6 characters" },
                })}
            />

            <button
                type="submit"
                className="bg-m-dark text-white w-full rounded-lg py-3 cursor-pointer"
            >
                Continue
            </button>

            <p className="text-center text-sm">
                Don't have an account?{" "}
                <a href="/register" className="text-m-dark font-semibold hover:underline">
                    Sign up
                </a>
            </p>
        </form>
    );
}

export default LoginForm;