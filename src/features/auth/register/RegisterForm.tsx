import TextField from "@/shared/ui/fields/TextField.tsx";
import CountrySelect from "@/shared/ui/fields/CountrySelectField.tsx";
import PasswordField from "@/shared/ui/fields/PasswordField.tsx";
import CheckboxCard from "@/shared/ui/fields/CheckboxCard.tsx";
import {useForm} from "react-hook-form";
import {useDispatch} from "react-redux";
import {useNavigate} from "react-router-dom";
import {useEffect} from "react";
import {register as registerThunk, resetRegistration} from "@/entities/auth/model/authSlice.ts";


interface RegisterForm {
    // accountType: "corporate" | "individual";
    company_trading_name?: string;
    country: string;
    contact_phone: string;
    email: string;
    password: string;
    selected_product?: string[];
    heardFrom?: string;
    termsAccepted: boolean;
}


const serviceOptions = [
    { label: "Banking Services", value: "banking" },
    { label: "Payment Services", value: "payment" },
];

function RegisterForm() {

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        clearErrors,
        formState: { errors },
    } = useForm<RegisterForm>();
    const dispatch = useDispatch();

    const navigate = useNavigate();

    useEffect(() => {
        dispatch(resetRegistration());
    }, [dispatch]);


    const onSubmit = async data => {
        try {
            await dispatch(registerThunk(data)).unwrap();
            navigate("/verify-email");
        } catch {
        }
    };



    const fillTestData = () => {
        setValue("company_trading_name", "kana");
        setValue("country", "123");
        setValue("email", "<EMAIL>");
        setValue("contact_phone", "********");
        setValue("password", "*********");
        setValue("selected_product", "banking");
        setValue("termsAccepted", true);
        clearErrors();
    };

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="w-full max-w-lg p-6 space-y-4">
            <h1 className="text-center text-xl font-semibold pb-4 border-b border-m-dark/15">
                Sign up
            </h1>
            {/*<button*/}
            {/*    type="button"*/}
            {/*    onClick={fillTestData}*/}
            {/*    className="bg-gray-200 text-black w-full rounded-lg py-2 mb-2 cursor-pointer hover:bg-black hover:text-white"*/}
            {/*>*/}
            {/*    Fill test data*/}
            {/*</button>*/}

            <TextField
                label="Company trading name"
                name="company_trading_name"
                {...register("company_trading_name", { required: "Company name is required" })}
                error={errors.company_trading_name?.message}
            />
            <CountrySelect
                label="Country"
                name="country"
                {...register("country", { required: "Сountry name is required" })}
                error={errors.country?.message}
                setValue={(name, value) => {
                    setValue(name, value);
                    clearErrors(name); // Сбрасываем ошибку при выборе страны
                }}
                watch={watch}
            />
            <TextField
                label="Contact phone"
                name="contact_phone"
                {...register("contact_phone", { required: "Phone is required" })}
                error={errors.contact_phone?.message}
            />
            <TextField
                label="Email"
                name="email"
                error={errors.email?.message}
                {...register("email", { required: "Email is required", pattern: { value: /\S+@\S+\.\S+/, message: "Invalid email address" } })}
            />
            <PasswordField
                label="Password"
                name="password"
                {...register("password",{
                    required: "Password is required",
                    minLength: { value: 6, message: "Minimum 6 characters" },
                })}
                error={errors.password?.message}
            />

            <div className="space-y-2">
                {serviceOptions.map(({ label, value }) => (
                    <CheckboxCard
                        key={value}
                        name="selected_product"
                        value={value}
                        label={label}
                        selected={watch("selected_product") === value}
                        onChange={() => setValue("selected_product", value)}
                    />
                ))}
            </div>
            <button type="submit" className="bg-black text-white w-full rounded-lg py-3 cursor-pointer dark:bg-m-orange">
                Submit
            </button>
            <p className="text-center text-sm">Already have an account?{" "}
                <a href="/login" className="text-m-dark font-semibold hover:underline">
                    Sign in
                </a>
            </p>
        </form>
    );
}

export default RegisterForm;