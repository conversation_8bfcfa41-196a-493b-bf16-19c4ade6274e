import { useState, useEffect } from "react";

export const useSearch = (items: any[], searchField: string) => {
    const [query, setQuery] = useState("");
    const [filteredItems, setFilteredItems] = useState(items);

    useEffect(() => {
        if (query) {
            setFilteredItems(
                items.filter(item =>
                    item[searchField].toLowerCase().includes(query.toLowerCase())
                )
            );
        } else {
            setFilteredItems(items);
        }
    }, [query, items, searchField]);

    return { query, setQuery, filteredItems };
};
