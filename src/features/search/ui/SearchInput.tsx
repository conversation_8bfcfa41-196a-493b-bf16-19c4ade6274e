import React, { useState } from "react";
import { FiSearch } from "react-icons/fi";

interface SearchInputProps {
    onSearch: (query: string) => void;
}

export const SearchInput: React.FC<SearchInputProps> = ({ onSearch }) => {
    const [query, setQuery] = useState("");

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setQuery(e.target.value);
        onSearch(e.target.value);
    };

    return (
        <div className="flex items-center bg-white dark:bg-m-dark  rounded-full px-4 py-2 w-full">
            <input
                type="text"
                value={query}
                onChange={handleChange}
                placeholder="Search"
                className="bg-transparent w-full outline-none text-m-dark dark:text-white"
            />
            <FiSearch className="text-gray-500" size={20} />
        </div>
    );
};
