import { useLocation } from "react-router-dom";
import ThemeSwitcher from "@/features/themeSwitcher/ui";
import {NotificationDropdown} from "@/features/notifications/ui/NotificationsDropdown.tsx";
import QuickActions from "@/features/quickActions/ui/QuickActions.tsx";

const pageTitles: Record<string, string> = {
    "/overview": "Overview",
    "/accounts": "Accounts",
    "/cards": "Cards",
    "/transactions": "Transactions",
    "/invoices": "Invoices",
    "/iban": "IBAN",
    "/user-management": "User Management",
    "/profile-settings": "Profile Settings",
    "/verification": "Verification KYC and KYB",
};

const Header: React.FC = () => {
    const location = useLocation();
    const title: string = pageTitles[location.pathname] || "Overview";

    return (
        <nav className="rounded-lg px-8 pt-7 flex items-center justify-between">
            <h1 className="text-3xl font-semibold dark:text-white">{title}</h1>
            <div className="flex items-center gap-3">
                <ThemeSwitcher />
                <QuickActions/>
                <NotificationDropdown />
            </div>
        </nav>
    );
};

export default Header;
