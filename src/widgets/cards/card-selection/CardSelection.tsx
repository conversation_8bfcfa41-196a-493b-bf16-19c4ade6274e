import React from "react";

const CardsSelection: React.FC = () => {
    return (
        <div className="bg-black p-6 rounded-xl w-1/2 mx-auto text-white">
            <h2 className="text-lg font-semibold mb-7 text-center">Coming soon...</h2>
            <div className=" flex justify-center items-center">
                <img
                    src='/images/card/card1.svg'
                    alt='Card Image'
                    className="w-[480px] object-cover rounded-xl shadow-lg"
                />
            </div>
            {/*<button className="w-full mt-6 bg-m-orange py-3 rounded-lg text-black font-medium cursor-pointer hover:bg-m-orange-dark">*/}
            {/*    Continue*/}
            {/*</button>*/}
        </div>
    );
};

export default CardsSelection;
