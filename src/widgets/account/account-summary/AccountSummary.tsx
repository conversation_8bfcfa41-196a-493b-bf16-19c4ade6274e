import {ActionButton} from "@/shared/ui/buttons/ActionButtons.tsx";


const actions = [
    { icon: "/images/icons/plusBalance.svg", label: "Deposit" },
    { icon: "/images/icons/exchange.svg", label: "Exchange" },
    { icon: "/images/icons/upBalance.svg", label: "Send" },
];


function AccountSummary(props) {
    return (
        <div className='bg-white dark:bg-m-dark w-full h-max rounded-xl px-7 py-6'>
            <div className='flex justify-between items-center'>
                <h2 className='font-semibold text-xl dark:text-white'>Account details</h2>
                <div className="flex space-x-2 justify-end">
                    {actions.map(({ icon, label }) => (
                        <ActionButton key={label} icon={icon} label={label} />
                    ))}
                </div>
            </div>
            <div className="divide-y divide-dashed pt-7">
                <div className="py-2">
                    <span className="block text-sm text-gray-500">Account name</span>
                    <span className="text-lg dark:text-white">Primary account</span>
                </div>
                <div className="py-2">
                    <span className="block text-sm text-gray-500">Currency</span>
                    <span className="text-lg dark:text-white">EUR</span>
                </div>
                <div className="py-2">
                    <span className="block text-sm text-gray-500">ID</span>
                    <span className="text-lg dark:text-white">123</span>
                </div>
            </div>
        </div>
    );
}

export default AccountSummary;