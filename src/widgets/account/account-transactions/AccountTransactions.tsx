import {TransactionList} from "@/features/transaction/ui/TransactionList.tsx";

interface Transaction {
    id: number;
    name: string;
    amount: string;
    date: string;
    icons: string;
}

const AccountTransactions = ({ transactions }: { transactions: Transaction[] }) => {
    return <TransactionList transactions={transactions} title="Latest transactions" />;
};

export default AccountTransactions;
