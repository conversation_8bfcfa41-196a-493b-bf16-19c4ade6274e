import {useState} from "react";
import {AccountCard} from "@/entities/account/ui/AccountCard.tsx";


const tabs = ["All accounts", "Crypto", "Flat"];

interface Account {
    id: string;
    balance: string;
}


function AccountsTabs({ accounts }: { accounts: Account[] }) {
    const [activeTab, setActiveTab] = useState("All accounts");

    return (
        <div className="bg-white rounded-xl p-4 dark:bg-m-dark">
            <div className="flex space-x-6 pb-2">
                {tabs.map((tab) => (
                    <button
                        key={tab}
                        className={`py-1 font-medium cursor-pointer dark:text-white ${
                            activeTab === tab ? "border-b-2 border-m-violet" : "text-gray-500"
                        }`}
                        onClick={() => setActiveTab(tab)}
                    >
                        {tab}
                    </button>
                ))}
            </div>
            <div className="flex space-x-4 mt-4">
                {accounts.map((account) => (
                    <AccountCard key={account.id} id={account.id} balance={account.balance} style="dark:bg-m-dark-muted" />
                ))}
            </div>
        </div>
    );
}

export default AccountsTabs;