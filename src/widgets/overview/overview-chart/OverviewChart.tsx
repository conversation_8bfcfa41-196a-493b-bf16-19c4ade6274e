import { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
import "chart.js/auto";

interface OverviewChartProps {
    chartData: number[];
}

const OverviewChart = ({ chartData }: OverviewChartProps) => {
    const [isDark, setIsDark] = useState(
        localStorage.getItem("theme") === "dark"
    );

    useEffect(() => {
        const checkDarkMode = () => {
            setIsDark(localStorage.getItem("theme") === "dark");
        };
        const observer = new MutationObserver(checkDarkMode);
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ["class"],
        });
        window.addEventListener("storage", checkDarkMode);
        return () => {
            observer.disconnect();
            window.removeEventListener("storage", checkDarkMode);
        };
    }, []);

    const data = {
        labels: ["Mon", "<PERSON>e", "Wed", "<PERSON>hu", "Fr<PERSON>", "Sat", "Sun"],
        datasets: [
            {
                label: "Balance",
                data: chartData,
                borderColor: isDark ? "#A78BFA" : "#5E2FEC",
                backgroundColor: isDark
                    ? "rgba(167, 139, 250, 0.2)"
                    : "rgba(94, 47, 236, 0.2)",
                fill: true,
                tension: 0.4,
                pointRadius: 0,
                pointHoverRadius: 6,
                pointHoverBackgroundColor: isDark ? "#A78BFA" : "#5E2FEC",
                pointHoverBorderColor: "#ffffff",
                pointHoverBorderWidth: 2,
                borderWidth: 1,

            },
        ],
    };

    const options = {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: { display: false },
        },
        interaction: {
            mode: "nearest",
            intersect: false,
        },
        hover: {
            mode: "nearest",
            intersect: false,
        },
        scales: {
            x: {
                grid: { display: false },
                ticks: { color: isDark ? "#D1D5DB" : "#374151" },
            },
            y: {
                grid: { display: true, color: isDark ? "#374151" : "#E5E7EB" },
                ticks: { color: isDark ? "#D1D5DB" : "#374151" },
            },
        },
    };

    return (
        <div
            className={`p-4 rounded-lg shadow h-[300px] w-full ${
                isDark ? "bg-[#1F1F1F]" : "bg-white"
            }`}
        >
            <Line data={data} options={options} />
        </div>
    );
};

export default OverviewChart;
