import {AccountCard} from "@/entities/account/ui/AccountCard.tsx";

interface Account {
    id: string;
    balance: string;
}

const OverviewAccounts = ({ accounts }: { accounts: Account[] }) => {
    return (
        <div className='w-full'>
            <div className='flex items-center justify-between mb-3'>
                <h2 className="text-xl font-semibold dark:text-white">My Accounts</h2>
                <p className="flex border px-3 py-1 rounded-2xl cursor-pointer dark:border-white/65">
                    <span className='dark:text-white'>More</span>
                    <img className='rotate-45 dark:invert' src='/images/icons/upBalance.svg' alt='icons'/>
                </p>
            </div>
            <div className="flex space-x-4 justify-between">
                {accounts.map((acc) => (
                    <AccountCard key={acc.id} id={acc.id} balance={acc.balance} />
                ))}
            </div>
        </div>
    );
};

export default OverviewAccounts;
