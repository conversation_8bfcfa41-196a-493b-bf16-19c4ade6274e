import {ActionButton} from "@/shared/ui/buttons/ActionButtons.tsx";

interface OverviewBalanceProps {
    balance: string;
}

const actions = [
    { icon: "/images/icons/plusBalance.svg", label: "Deposit" },
    { icon: "/images/icons/exchange.svg", label: "Exchange" },
    { icon: "/images/icons/upBalance.svg", label: "Send" },
];

const OverviewBalance = ({ balance }: OverviewBalanceProps) => {
    return (
        <div
            className="p-7 rounded-xl text-white "
            style={{
                background: "linear-gradient(to right, #D46076 45%, #5E2FEC 100%)",
            }}
        >
            <h2 className="text-lg font-medium">All balances</h2>
            <div className="flex justify-between items-center mt-15">
                <p className="text-4xl font-semibold">
                    {balance}
                </p>
                <div className="flex space-x-2 justify-end">
                    {actions.map(({ icon, label }) => (
                        <ActionButton key={label} icon={icon} label={label} />
                    ))}
                </div>
            </div>
        </div>
    );
};

export default OverviewBalance;
