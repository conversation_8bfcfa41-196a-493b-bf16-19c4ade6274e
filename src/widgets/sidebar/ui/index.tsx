import SidebarItem from "@/widgets/sidebar/ui/SidebarItems.tsx";
import SidebarUser from "@/widgets/sidebar/ui/SidebarUser.tsx";
import {sidebarLinks} from "@/widgets/sidebar/model/sidebarLinks.ts";
import { useNavigate } from "react-router-dom";
import SidebarVerifications from "@/features/verification/ui/SidebarVerifications.tsx";
import {useDispatch, useSelector} from "react-redux";
import {RootState} from "@/app/store.ts";
import {logout} from "@/entities/auth/model/authSlice.ts";

function SideBar() {
    const dispatch = useDispatch();
    const navigation = useNavigate();

    const { verificationStatus } = useSelector((state: RootState) => state.verification);

    const handleLogout = () => {
        dispatch(logout());
        navigation('/login')
    }

    return (
        <nav className='w-80 bg-m-dark h-screen flex flex-col overflow-y-auto'>
            <div className='p-7 border-b border-b-m-white/25'>
                <img src='/images/logo/mainLogo.svg' alt='logo'/>
            </div>
            <SidebarUser  />
            <nav className="flex-grow">
                <ul className="space-y-4 px-8">
                    {sidebarLinks.map((item) => (
                        <SidebarItem key={item.path} {...item} />
                    ))}
                </ul>
            </nav>
            {verificationStatus !== "Verified" && <SidebarVerifications />}
            <div className="mt-auto px-8 pb-10">
                <div className='text-red-500 flex items-center gap-2 cursor-pointer' onClick={handleLogout}>
                    <img src='/images/icons/logout.svg' alt='img'/>
                    <span className="material-icons text-base">Log out</span>
                </div>
            </div>
        </nav>
    );
}

export default SideBar;