import { NavLink } from "react-router-dom";

const SidebarItem = ({ path, label, icon }) => {
    return (
        <li>
            <NavLink
                to={path}
                className={({ isActive }) =>
                    `flex items-center space-x-3 p-2 rounded-lg transition ${
                        isActive ? "bg-m-pink/10   text-m-pink" : "text-white hover:bg-m-pink/10 hover:text-m-pink"
                    }`
                }
            >
                <img src={icon} alt="icon"/>
                <span className="font-semibold text-base">{label}</span>
            </NavLink>
        </li>
    );
};

export default SidebarItem;
